GEM
  remote: https://rubygems.org/
  specs:
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.3)
    base64 (0.2.0)
    bigdecimal (3.1.9)
    cloud_events (0.7.1)
    crack (1.0.0)
      bigdecimal
      rexml
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    diff-lcs (1.6.2)
    docile (1.4.1)
    erb (5.0.2)
    functions_framework (1.6.2)
      cloud_events (>= 0.7.0, < 2.a)
      puma (>= 4.3.0, < 7.a)
      rack (>= 2.1, < 4.a)
    hashdiff (1.1.2)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    json (2.13.2)
    language_server-protocol (********)
    lint_roller (1.1.0)
    mini_portile2 (2.8.9)
    nio4r (2.7.4)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.6.1)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (3.2.0)
    rainbow (3.1.1)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.11.0)
    reline (0.6.2)
      io-console (~> 0.5)
    rexml (3.4.1)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-support (3.13.4)
    rubocop (1.79.1)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.46.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.46.0)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-progressbar (1.13.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.1)
    simplecov_json_formatter (0.1.4)
    stringio (3.1.7)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    vcr (6.3.1)
      base64
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)

PLATFORMS
  ruby

DEPENDENCIES
  debug (~> 1.11)
  functions_framework (~> 1.6)
  nokogiri (~> 1.18)
  rspec (~> 3.13)
  rubocop (~> 1.79)
  rubocop-performance (~> 1.25)
  rubocop-rspec (~> 3.6)
  simplecov (~> 0.22)
  simplecov_json_formatter (~> 0.1.4)
  vcr (~> 6.3)
  webmock (~> 3.25)

RUBY VERSION
   ruby 3.2.4p170

BUNDLED WITH
   2.4.19
