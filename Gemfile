# frozen_string_literal: true

source 'https://rubygems.org'

ruby '~> 3.2.3'

gem 'functions_framework', '~> 1.6'
gem 'nokogiri', '~> 1.18', platform: :ruby

group :development, :test do
  gem 'debug', '~> 1.11'
end

group :development do
  # Source quality
  gem 'rubocop', '~> 1.79'
  gem 'rubocop-performance', '~> 1.25'
  gem 'rubocop-rspec', '~> 3.6'
end

group :test do
  # Test framework
  gem 'rspec', '~> 3.13'

  # Code coverage for Ruby
  gem 'simplecov', '~> 0.22', require: false
  gem 'simplecov_json_formatter', '~> 0.1.4'

  # Record your test suite's HTTP interactions
  gem 'vcr', '~> 6.3'
  gem 'webmock', '~> 3.25'
end
