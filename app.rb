# frozen_string_literal: true

require 'cgi'
require 'functions_framework'
require 'json'
require 'nokogiri'
require 'resolv-replace'
require 'open-uri'

BASE_URL = 'https://joaobidu.com.br/horoscopo-do-dia/horoscopo-do-dia-para-'

SIGN_EMOJIS = {
  'aquario' => ':aquarius:',
  'aries' => ':aries:',
  'cancer' => ':cancer:',
  'capricornio' => ':capricorn:',
  'escorpiao' => ':scorpius:',
  'gemeos' => ':gemini:',
  'leao' => ':leo:',
  'libra' => ':libra:',
  'peixes' => ':pisces:',
  'sagitario' => ':sagittarius:',
  'touro' => ':taurus:',
  'virgem' => ':virgo:'
}.freeze
CHANNELS_URL = {
  'C06A84LMNN8' => URI.parse('*******************************************************************************'),
  'C027EHYCM' => URI.parse('*******************************************************************************')
}.freeze
EVENT_TYPE_APP_MENTION = 'app_mention'
HEADERS_HTTP_POST = { 'content-type': 'application/json' }.freeze
ACCENTS_REPLACEMENT = {
  %w[á à â ä ã] => 'a',
  %w[Ã Ä Â À] => 'A',
  %w[é è ê ë] => 'e',
  %w[Ë É È Ê] => 'E',
  %w[í ì î ï] => 'i',
  %w[Î Ì] => 'I',
  %w[ó ò ô ö õ] => 'o',
  %w[Õ Ö Ô Ò Ó] => 'O',
  %w[ú ù û ü] => 'u',
  %w[Ú Û Ù Ü] => 'U',
  %w[ç] => 'c',
  %w[Ç] => 'C',
  %w[ñ] => 'n',
  %w[Ñ] => 'N'
}.freeze

# rubocop:disable Layout/LineLength
USER_AGENTS = [
  'Mozilla/5.0 (Linux; U; Android 4.0.2; en-us; Galaxy Nexus Build/ICL53F) AppleWebKit/534.30 (KHTML, like Gecko) Version/4.0 Mobile Safari/534.30',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.97 Safari/537.36 OPR/65.0.3467.48',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Safari/605.1.15',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36 Edg/131.0.100.0'
].freeze
# rubocop:enable Layout/LineLength

def horoscope_for(sign)
  ACCENTS_REPLACEMENT.each do |accent, replacement|
    accent.each { |letter| sign = sign.gsub(letter, replacement) }
  end
  sign = sign.downcase

  doc = Nokogiri::HTML(URI.open("#{BASE_URL}#{sign}/", 'User-Agent' => USER_AGENTS.sample))
  elements = doc.xpath('/html/body/main/div/section/div[2]/div[1]/div[2]/div[1]/article/p')

  [sign, elements.to_a.join("\n")]
end

def slack_content_formater(sign, horoscope)
  horoscope = horoscope.gsub(/^/, '> ').gsub("\n", "\n> \n").strip
  content = "Segue o horóscopo de hoje para #{sign} #{SIGN_EMOJIS[sign]}:\n\n#{horoscope}\n\n" \
            'Fonte: <https://joaobidu.com.br|João Bidu>'

  [sign, content]
end

FunctionsFramework.http 'sign' do |request|
  raw_body = request.body.read
  body = begin
    JSON.parse(raw_body)
  rescue StandardError
    CGI.parse(raw_body)
  end

  # Response to Slack challenge for webhook verification
  if body['challenge'] && !body['challenge'].empty?
    body['challenge']

  # Response to Slack public command with mention (`@John Beedoo [sign]`)
  elsif body['event'].is_a?(Hash) && body.dig('event', 'type') == EVENT_TYPE_APP_MENTION
    _sign, horoscope = slack_content_formater(*horoscope_for(body['event']['text'].split.last))
    data = { text: "<@#{body['event']['user']}> #{horoscope}" }.to_json
    response = Net::HTTP.post(CHANNELS_URL[body['event']['channel']], data, HEADERS_HTTP_POST)
    response.body

  # response to Slack private command (`/horóscopo [sign]`)
  else
    slack_content_formater(*horoscope_for(body['text'].first)).last
  end
end
