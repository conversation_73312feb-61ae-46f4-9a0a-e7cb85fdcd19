# frozen_string_literal: true

if ENV.fetch('COVERAGE', 'false') == 'true'
  require 'simplecov'
  require 'simplecov_json_formatter'

  SimpleCov.formatters = SimpleCov::Formatter::MultiFormatter.new([
                                                                    SimpleCov::Formatter::HTMLFormatter,
                                                                    SimpleCov::Formatter::JSONFormatter
                                                                  ])
  SimpleCov.start do
    enable_coverage :branch
  end
end

require 'rspec'
require 'functions_framework/testing'
require 'vcr'

RSpec.configure do |config|
  config.include FunctionsFramework::Testing, type: :function
  config.order = :random
  config.profile_examples = 3
end

VCR.configure do |config|
  config.cassette_library_dir = 'spec/fixtures/vcr_cassettes'
  config.hook_into :webmock
  config.configure_rspec_metadata!
  config.default_cassette_options = {
    record: :once,
    match_requests_on: %i[method uri body]
  }
  config.allow_http_connections_when_no_cassette = false
end
