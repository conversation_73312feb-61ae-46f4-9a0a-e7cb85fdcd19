---
http_interactions:
- request:
    method: get
    uri: https://joaobidu.com.br/horoscopo-do-dia/horoscopo-do-dia-para-virgem/
    body:
      encoding: US-ASCII
      string: ''
    headers:
      User-Agent:
      - Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like
        Gecko) Chrome/********* Safari/537.36 Edg/***********
      Accept-Encoding:
      - gzip;q=1.0,deflate;q=0.6,identity;q=0.3
      Accept:
      - "*/*"
  response:
    status:
      code: 200
      message: OK
    headers:
      Server:
      - nginx
      Date:
      - Fri, 06 Jun 2025 12:35:29 GMT
      Content-Type:
      - text/html; charset=UTF-8
      Content-Length:
      - '70877'
      Connection:
      - keep-alive
      X-Powered-By:
      - PHP/8.2.18
      Vary:
      - Accept-Encoding
      X-Cache-Status:
      - HIT
      X-Powered-Cache-By:
      - Astral Digital
    body:
      encoding: ASCII-8BIT
      string: !binary |-
        PCEtLQ0KPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09DQoqIFlFTExPV0VCIEpCIFRFTVBMQVRFIC0gdjIuMS4wDQo9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0NCiogUHJvZHVjdCBQYWdlOiBodHRwczovL3llbGxvd2ViLmNvbS5icg0KKiBDb2RlZCBieSBQYXVsbyBCcmFnYSAtIHBhdWxvaGIuYW1vcmltQGhvdG1haWwuY29tDQo9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0NCi0tPg0KPCFkb2N0eXBlIGh0bWw+DQo8aHRtbCBsYW5nPSJwdC1CUiI+DQo8aGVhZD4NCgk8bWV0YSBjaGFyc2V0PSJ1dGYtOCI+DQoJPG1ldGEgbmFtZT0idmlld3BvcnQiIGNvbnRlbnQ9IndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xIj4NCg0KICAgIDx0aXRsZT5Ib3LDs3Njb3BvIGRvIERpYSBwYXJhIFZpcmdlbSAtIEpvw6NvIEJpZHU8L3RpdGxlPg0KICAgIDxtZXRhIG5hbWU9ImRlc2NyaXB0aW9uIiBjb250ZW50PSJIb3LDs3Njb3BvIGRvIHNpZ25vIGRlIFZpcmdlbSwgY29tIGEgcHJldmlzw6NvIGNvbXBsZXRhIGRlIEpvw6NvIEJpZHUgcGFyYSBvIHNldSBzaWdubyBuYSBzZW1hbmEuIFZlamEgbyBxdWUgb3MgYXN0cm9zIHJlc2VydmFtIHBhcmEgdm9jw6oiIC8+DQogICAgPG1ldGEgbmFtZT0icm9ib3RzIiBjb250ZW50PSJpbmRleCwgZm9sbG93LCBtYXgtc25pcHBldDotMSwgbWF4LWltYWdlLXByZXZpZXc6bGFyZ2UsIG1heC12aWRlby1wcmV2aWV3Oi0xIiAvPg0KICAgIDxsaW5rIHJlbD0iY2Fub25pY2FsIiBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS12aXJnZW0vIiAvPg0KICAgIDxsaW5rIHJlbD0iYW1waHRtbCIgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvL3NpZ25vcy9wcmV2aXNhby12aXJnZW0vYW1wLyIgLz4NCg0KICAgIDxtZXRhIHByb3BlcnR5PSJvZzpsb2NhbGUiIGNvbnRlbnQ9InB0X0JSIiAvPg0KICAgIDxtZXRhIHByb3BlcnR5PSJvZzp0eXBlIiBjb250ZW50PSJhcnRpY2xlIiAvPg0KICAgIDxtZXRhIHByb3BlcnR5PSJvZzp0aXRsZSIgY29udGVudD0iSG9yw7NzY29wbyBkbyBEaWEgcGFyYSBWaXJnZW0iIC8+DQogICAgPG1ldGEgcHJvcGVydHk9Im9nOmRlc2NyaXB0aW9uIiBjb250ZW50PSJIb3LDs3Njb3BvIGRvIHNpZ25vIGRlIFZpcmdlbSwgY29tIGEgcHJldmlzw6NvIGNvbXBsZXRhIGRlIEpvw6NvIEJpZHUgcGFyYSBvIHNldSBzaWdubyBuYSBzZW1hbmEuIFZlamEgbyBxdWUgb3MgYXN0cm9zIHJlc2VydmFtIHBhcmEgdm9jw6oiIC8+DQogICAgPG1ldGEgcHJvcGVydHk9Im9nOnVybCIgY29udGVudD0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtdmlyZ2VtLyIgLz4NCiAgICA8bWV0YSBwcm9wZXJ0eT0ib2c6c2l0ZV9uYW1lIiBjb250ZW50PSJKb8OjbyBCaWR1IiAvPg0KICAgIDxtZXRhIHByb3BlcnR5PSJhcnRpY2xlOm1vZGlmaWVkX3RpbWUiIGNvbnRlbnQ9IjIwMjUtMDYtMDZUMjE6MDA6MDAtMDM6MDAiIC8+DQogICAgPG1ldGEgbmFtZT0idHdpdHRlcjpjYXJkIiBjb250ZW50PSJzdW1tYXJ5X2xhcmdlX2ltYWdlIiAvPg0KDQogICAgPCEtLSBHb29nbGUgVGFnIE1hbmFnZXIgLS0+DQogICAgPHNjcmlwdCB0eXBlPSJsYXp5c2NyaXB0Ij4NCiAgICAgICAgKGZ1bmN0aW9uKHcsIGQsIHMsIGwsIGkpIHsNCiAgICAgICAgICAgIHdbbF0gPSB3W2xdIHx8IFtdOw0KICAgICAgICAgICAgd1tsXS5wdXNoKHsNCiAgICAgICAgICAgICAgICAnZ3RtLnN0YXJ0JzogbmV3IERhdGUoKS5nZXRUaW1lKCksDQogICAgICAgICAgICAgICAgZXZlbnQ6ICdndG0uanMnDQogICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIHZhciBmID0gZC5nZXRFbGVtZW50c0J5VGFnTmFtZShzKVswXSwNCiAgICAgICAgICAgICAgICBqID0gZC5jcmVhdGVFbGVtZW50KHMpLA0KICAgICAgICAgICAgICAgIGRsID0gbCAhPSAnZGF0YUxheWVyJyA/ICcmbD0nICsgbCA6ICcnOw0KICAgICAgICAgICAgai5hc3luYyA9IHRydWU7DQogICAgICAgICAgICBqLnNyYyA9DQogICAgICAgICAgICAgICAgJ2h0dHBzOi8vd3d3Lmdvb2dsZXRhZ21hbmFnZXIuY29tL2d0bS5qcz9pZD0nICsgaSArIGRsOw0KICAgICAgICAgICAgZi5wYXJlbnROb2RlLmluc2VydEJlZm9yZShqLCBmKTsNCiAgICAgICAgfSkod2luZG93LCBkb2N1bWVudCwgJ3NjcmlwdCcsICdkYXRhTGF5ZXInLCAnR1RNLU5KUVdDQjQnKTsNCiAgICA8L3NjcmlwdD4NCg0KICAgIDxsaW5rIHJlbD0ncHJlY29ubmVjdCcgaHJlZj0nLy9mb250cy5nb29nbGVhcGlzLmNvbScgY3Jvc3NvcmlnaW4gLz4NCiAgICA8bGluayByZWw9ImRucy1wcmVmZXRjaCIgaHJlZj0iaHR0cHM6Ly93d3cuZ29vZ2xldGFnbWFuYWdlci5jb20iPg0KICAgIDxsaW5rIHJlbD0iZG5zLXByZWZldGNoIiBocmVmPSJodHRwczovL3d3dy5nb29nbGUtYW5hbHl0aWNzLmNvbS8iPg0KDQogICAgPGxpbmsgcmVsPSdkbnMtcHJlZmV0Y2gnIGhyZWY9Jy8vcy53Lm9yZycgLz4NCiAgICA8bGluayByZWw9ImFsdGVybmF0ZSIgdHlwZT0iYXBwbGljYXRpb24vcnNzK3htbCIgdGl0bGU9IkZlZWQgcGFyYSBKb8OjbyBCaWR1IiBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9mZWVkIiAvPg0KICAgIDxsaW5rIHJlbD0iYWx0ZXJuYXRlIiB0eXBlPSJhcHBsaWNhdGlvbi9yc3MreG1sIiB0aXRsZT0iRmVlZCBkZSBjb21lbnTDoXJpb3MgcGFyYSBKb8OjbyBCaWR1IiBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9jb21tZW50cy9mZWVkIiAvPg0KDQogICAgPGxpbmsgcmVsPSdzdHlsZXNoZWV0JyBpZD0nbWFpbi1jc3MtY3NzJyBocmVmPSdodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvY3NzL3RoZW1lLm1pbi5jc3M/dj0xNjM0MjQyMjA3JyB0eXBlPSd0ZXh0L2NzcycgbWVkaWE9J2FsbCcgLz4NCiAgICANCiAgICAgICAgPHNjcmlwdCB0eXBlPSJhcHBsaWNhdGlvbi9sZCtqc29uIiBjbGFzcz0ieW9hc3Qtc2NoZW1hLWdyYXBoIj4NCiAgICAgICAgICAgIHsNCiAgICAgICAgICAgICAgICAiQGNvbnRleHQiOiAiaHR0cDovL3NjaGVtYS5vcmciLA0KICAgICAgICAgICAgICAgICJAdHlwZSI6ICJOZXdzQXJ0aWNsZSIsDQogICAgICAgICAgICAgICAgIm1haW5FbnRpdHlPZlBhZ2UiOiB7DQogICAgICAgICAgICAgICAgICAgICJAdHlwZSI6ICJXZWJQYWdlIiwNCiAgICAgICAgICAgICAgICAgICAgIkBpZCI6ICJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS12aXJnZW0vIiwNCiAgICAgICAgICAgICAgICAgICAgIm5hbWUiOiAiSG9yw7NzY29wbyBkbyBEaWEgcGFyYSBWaXJnZW0iDQogICAgICAgICAgICAgICAgfSwNCiAgICAgICAgICAgICAgICAiaGVhZGxpbmUiOiAiSG9yw7NzY29wbyBkbyBEaWEgcGFyYSBWaXJnZW0iLA0KICAgICAgICAgICAgICAgICJ1cmwiOiAiaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtdmlyZ2VtLyIsDQogICAgICAgICAgICAgICAgImltYWdlIjogW3sNCiAgICAgICAgICAgICAgICAgICAgIkB0eXBlIjogIkltYWdlT2JqZWN0IiwNCiAgICAgICAgICAgICAgICAgICAgInVybCI6ICJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaW1hZ2VzL3pvZGlhYy92aXJnZW0uc3ZnIiwNCiAgICAgICAgICAgICAgICAgICAgIndpZHRoIjogIjEwMjQiLA0KICAgICAgICAgICAgICAgICAgICAiaGVpZ2h0IjogIjQwMCINCiAgICAgICAgICAgICAgICB9XSwNCg0KICAgICAgICAgICAgICAgICJkYXRlUHVibGlzaGVkIjogIjIwMjUtMDYtMDZUMDA6MDE6MDAtMDM6MDAiLA0KICAgICAgICAgICAgICAgICJkYXRlTW9kaWZpZWQiOiAiMjAyNS0wNi0wNlQyMTowMDowMC0wMzowMCIsDQoNCiAgICAgICAgICAgICAgICAiYXV0aG9yIjogew0KICAgICAgICAgICAgICAgICAgICAiQHR5cGUiOiAiUGVyc29uIiwNCiAgICAgICAgICAgICAgICAgICAgIm5hbWUiOiAiSm/Do28gQmlkdSIsDQogICAgICAgICAgICAgICAgICAgICJ1cmwiOiAiaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvYXV0aG9yL3JlZGFjYW8iLA0KICAgICAgICAgICAgICAgICAgICAic2FtZUFzIjogWyJodHRwczovL3R3aXR0ZXIuY29tL0pvYW9CaWR1IiwgImh0dHBzOi8vd3d3Lmluc3RhZ3JhbS5jb20vam9hb2JpZHUub2ZpY2lhbC8iXQ0KICAgICAgICAgICAgICAgIH0sDQoNCiAgICAgICAgICAgICAgICAicHVibGlzaGVyIjogew0KICAgICAgICAgICAgICAgICAgICAiQHR5cGUiOiAiT3JnYW5pemF0aW9uIiwNCiAgICAgICAgICAgICAgICAgICAgIm5hbWUiOiAiSm/Do28gQmlkdSIsDQogICAgICAgICAgICAgICAgICAgICJsb2dvIjogew0KICAgICAgICAgICAgICAgICAgICAgICAgIkB0eXBlIjogIkltYWdlT2JqZWN0IiwNCiAgICAgICAgICAgICAgICAgICAgICAgICJ1cmwiOiAiaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvYXNzZXRzL2xvZ28tamItaGVhZGVyLnBuZyIsDQogICAgICAgICAgICAgICAgICAgICAgICAid2lkdGgiOiAxMTEsDQogICAgICAgICAgICAgICAgICAgICAgICAiaGVpZ2h0IjogNTANCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgImNvcHlyaWdodEhvbGRlciI6IHsNCiAgICAgICAgICAgICAgICAgICAgIkB0eXBlIjogIk9yZ2FuaXphdGlvbiIsDQogICAgICAgICAgICAgICAgICAgICJuYW1lIjogIkpvw6NvIEJpZHUiLA0KICAgICAgICAgICAgICAgICAgICAibG9nbyI6IHsNCiAgICAgICAgICAgICAgICAgICAgICAgICJAdHlwZSI6ICJJbWFnZU9iamVjdCIsDQogICAgICAgICAgICAgICAgICAgICAgICAidXJsIjogImh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2Fzc2V0cy9sb2dvLWpiLWhlYWRlci5wbmciLA0KICAgICAgICAgICAgICAgICAgICAgICAgIndpZHRoIjogMTExLA0KICAgICAgICAgICAgICAgICAgICAgICAgImhlaWdodCI6IDYwDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9LA0KICAgICAgICAgICAgICAgICJhcnRpY2xlU2VjdGlvbiI6ICJIb3Jvc2NvcG8iLA0KICAgICAgICAgICAgICAgICJkZXNjcmlwdGlvbiI6ICJIb3LDs3Njb3BvIGRvIHNpZ25vIGRlIFZpcmdlbSwgY29tIGEgcHJldmlzw6NvIGNvbXBsZXRhIGRlIEpvw6NvIEJpZHUgcGFyYSBvIHNldSBzaWdubyBuYSBzZW1hbmEuIFZlamEgbyBxdWUgb3MgYXN0cm9zIHJlc2VydmFtIHBhcmEgdm9jw6oiDQogICAgICAgICAgICB9DQogICAgICAgIDwvc2NyaXB0Pg0KICAgICAgICA8c2NyaXB0IHR5cGU9ImFwcGxpY2F0aW9uL2xkK2pzb24iPg0KICAgICAgICAgICAgew0KICAgICAgICAgICAgICAgICJAY29udGV4dCI6ICJodHRwczovL3NjaGVtYS5vcmciLA0KICAgICAgICAgICAgICAgICJAdHlwZSI6ICJCcmVhZGNydW1iTGlzdCIsDQogICAgICAgICAgICAgICAgIml0ZW1MaXN0RWxlbWVudCI6IFt7DQogICAgICAgICAgICAgICAgICAgICJAdHlwZSI6ICJMaXN0SXRlbSIsDQogICAgICAgICAgICAgICAgICAgICJwb3NpdGlvbiI6IDEsDQogICAgICAgICAgICAgICAgICAgICJuYW1lIjogIkhvbWUiLA0KICAgICAgICAgICAgICAgICAgICAiaXRlbSI6ICJodHRwczovL2pvYW9iaWR1LmNvbS5ici8iDQogICAgICAgICAgICAgICAgfSwgew0KICAgICAgICAgICAgICAgICAgICAiQHR5cGUiOiAiTGlzdEl0ZW0iLA0KICAgICAgICAgICAgICAgICAgICAicG9zaXRpb24iOiAyLA0KICAgICAgICAgICAgICAgICAgICAibmFtZSI6ICJIb3Jvc2NvcG8iLA0KICAgICAgICAgICAgICAgICAgICAiaXRlbSI6ICJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhLyINCiAgICAgICAgICAgICAgICB9LCB7DQogICAgICAgICAgICAgICAgICAgICJAdHlwZSI6ICJMaXN0SXRlbSIsDQogICAgICAgICAgICAgICAgICAgICJwb3NpdGlvbiI6IDMsDQogICAgICAgICAgICAgICAgICAgICJuYW1lIjogIkhvcsOzc2NvcG8gZG8gRGlhIHBhcmEgVmlyZ2VtIiwNCiAgICAgICAgICAgICAgICAgICAgIml0ZW0iOiAiaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtdmlyZ2VtLyINCiAgICAgICAgICAgICAgICB9XQ0KICAgICAgICAgICAgfQ0KICAgICAgICA8L3NjcmlwdD4NCiAgICAgICAgPGxpbmsgcmVsPSJwcmVsb2FkIiBhcz0iaW1hZ2UiIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pbWFnZXMvem9kaWFjL3ZpcmdlbS5zdmciIHNyY3NldD0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ltYWdlcy96b2RpYWMvdmlyZ2VtLnN2ZyAxMDI0dywgaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ltYWdlcy96b2RpYWMvdmlyZ2VtLnN2ZyAzMDB3LCBodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaW1hZ2VzL3pvZGlhYy92aXJnZW0uc3ZnIDc2OHcsIGh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pbWFnZXMvem9kaWFjL3ZpcmdlbS5zdmcgMTI4MHciIHNpemVzPSIobWF4LXdpZHRoOiAxMDI0cHgpIDEwMHZ3LCAxMDI0cHgiPg0KICAgIDxsaW5rIHJlbD0nc3R5bGVzaGVldCcgaWQ9J29yYWNsZXMtY3NzJyBocmVmPWh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3llbGxvd2ViL2luY2x1ZGVzL29yYWNsZXMuY3NzP3Zlcj00LjYuMycgdHlwZT0ndGV4dC9jc3MnIG1lZGlhPSdhbGwnIC8+PC9oZWFkPg0KPGJvZHk+DQogICAgPCEtLSBHb29nbGUgVGFnIE1hbmFnZXIgKG5vc2NyaXB0KSAtLT4NCiAgICA8bm9zY3JpcHQ+PGlmcmFtZSBzcmM9Imh0dHBzOi8vd3d3Lmdvb2dsZXRhZ21hbmFnZXIuY29tL25zLmh0bWw/aWQ9R1RNLU5KUVdDQjQiDQogICAgaGVpZ2h0PSIwIiB3aWR0aD0iMCIgc3R5bGU9ImRpc3BsYXk6bm9uZTt2aXNpYmlsaXR5OmhpZGRlbiI+PC9pZnJhbWU+PC9ub3NjcmlwdD4NCiAgICA8IS0tIEVuZCBHb29nbGUgVGFnIE1hbmFnZXIgKG5vc2NyaXB0KSAtLT4NCiAgICA8ZGl2IGNsYXNzPSJjb250YWluZXItZmx1aWQgZml4ZWQtdG9wIiBpZD0idG9wLWJhciI+DQogICAgPGRpdiBjbGFzcz0iY29udGFpbmVyIHB5LTIgcHktbWQtMyI+DQogICAgICAgIDxkaXYgY2xhc3M9InJvdyBqdXN0aWZ5LWNvbnRlbnQtY2VudGVyIGFsaWduLWl0ZW1zLWNlbnRlciI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtMTIgdGV4dC1jZW50ZXIiPg0KICAgICAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vbWFwYWFzdHJhbC5qb2FvYmlkdS5jb20uYnIiPg0KICAgICAgICAgICAgICAgICAgICBKb8OjbyBCaWR1IHByZXBhcm91IHNldSA8c3BhbiBjbGFzcz0iZW1waGFzaXMiPk1hcGEgQXN0cmFsPC9zcGFuPjxiciBjbGFzcz0iZC1ibG9jayBkLW1kLW5vbmUiIC8+IENsaXF1ZSBlIHZlamEg4pyoDQogICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgIDwvZGl2Pg0KICAgIDwvZGl2Pg0KPC9kaXY+PGhlYWRlcj4NCiAgICA8ZGl2IGNsYXNzPSJjb250YWluZXIiPg0KICAgICAgICA8ZGl2IGNsYXNzPSJkLWZsZXggYWxpZ24taXRlbXMtY2VudGVyIGp1c3RpZnktY29udGVudC1iZXR3ZWVuIj4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0yIGQtYmxvY2sgZC1tZC1ub25lIj4NCiAgICAgICAgICAgICAgICA8aW1nIGNsYXNzPSJkLWJsb2NrIGQtbWQtbm9uZSBvcmRlci0wIG9yZGVyLWxnLTEiIHR5cGU9ImJ1dHRvbiIgDQogICAgICAgICAgICAgICAgICAgICBkYXRhLWJzLXRvZ2dsZT0ib2ZmY2FudmFzIiBkYXRhLWJzLXRhcmdldD0iI25hdk1haW5NZW51IiANCiAgICAgICAgICAgICAgICAgICAgIGFyaWEtY29udHJvbHM9Im5hdk1haW5NZW51IiBhcmlhLWxhYmVsPSJUb2dnbGUgbmF2aWdhdGlvbiINCiAgICAgICAgICAgICAgICAgICAgIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ljb25zL21lbnUuc3ZnIiAvPg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtNiBjb2wtbWQtOCI+DQogICAgICAgICAgICAgICAgPG5hdiBjbGFzcz0ibmF2YmFyIG5hdmJhci1leHBhbmQtbGciPg0KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb250YWluZXItZmx1aWQiPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGEgY2xhc3M9Im5hdmJhci1icmFuZCBteC1hdXRvIG9yZGVyLTEgb3JkZXItbGctMCBwZS1tZC0yIiANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5iciIgYXJpYS1sYWJlbD0iUMOhZ2luYSBpbmNpYWwiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaW1hZ2VzL2pvYW8tYmlkdS1pbnRlcm5hY2lvbmFsLWxvZ28uc3ZnIiANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD0iSm/Do28gQmlkdSIgLz4NCiAgICAgICAgICAgICAgICAgICAgICAgIDwvYT4NCiAgICAgICAgICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sbGFwc2UgbmF2YmFyLWNvbGxhcHNlIG9yZGVyLTIiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBpZD0ibWVudS1tZW51LWNhbmFpcy10b3BvIiBjbGFzcz0ibmF2YmFyLW5hdiBtYWluLW1lbnUiPjxsaSBjbGFzcz0ibmF2LWl0ZW0gZHJvcGRvd24iPjxhIGNsYXNzPSJuYXYtbGluayBkcm9wZG93bi10b2dnbGUiICBocmVmPSIvaG9yb3Njb3BvLyIgZGF0YS1icy10b2dnbGU9ImRyb3Bkb3duIiByb2xlPSJidXR0b24iIGFyaWEtZXhwYW5kZWQ9ImZhbHNlIj5Ib3LDs3Njb3BvPC9hPjx1bCBjbGFzcz0iZHJvcGRvd24tbWVudSI+PGxpIGNsYXNzPSJuYXYtaXRlbSI+PGEgY2xhc3M9Im5hdi1saW5rIiAgaHJlZj0iL2hvcm9zY29wby8iPkhvcsOzc2NvcG8gZG8gRGlhPC9hPjwvbGk+PGxpIGNsYXNzPSJuYXYtaXRlbSI+PGEgY2xhc3M9Im5hdi1saW5rIiAgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLXNlbWFuYWwvY29uZmlyYS1wcmV2aXNhby1zZXUtc2lnbm8ucGh0bWwiPkhvcsOzc2NvcG8gU2VtYW5hbDwvYT48L2xpPjxsaSBjbGFzcz0ibmF2LWl0ZW0iPjxhIGNsYXNzPSJuYXYtbGluayIgIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2NhbmFsL3ByZXZpc29lcy1kby1tZXMiPlByZXZpc8O1ZXMgZG8gTcOqczwvYT48L2xpPjxsaSBjbGFzcz0ibmF2LWl0ZW0iPjxhIGNsYXNzPSJuYXYtbGluayIgIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1hbW9yL3ByZXZpc2FvLWFtb3Itc2lnbm8tZmltLWRlLXNlbWFuYS5waHRtbCI+UHJldmlzw6NvIGRvIEFtb3I8L2E+PC9saT48bGkgY2xhc3M9Im5hdi1pdGVtIj48YSBjbGFzcz0ibmF2LWxpbmsiICBocmVmPSIvcGVyZmlsLWRvcy1zaWdub3MiPlBlcmZpbCBkb3MgU2lnbm9zPC9hPjwvbGk+PC91bD48L2xpPjxsaSBjbGFzcz0ibmF2LWl0ZW0iPjxhIGNsYXNzPSJuYXYtbGluayIgIGhyZWY9Imh0dHBzOi8vbWFwYWFzdHJhbC5qb2FvYmlkdS5jb20uYnIvIj5NYXBhIEFzdHJhbDwvYT48L2xpPjxsaSBjbGFzcz0ibmF2LWl0ZW0iPjxhIGNsYXNzPSJuYXYtbGluayIgIGhyZWY9Ii9vcmFjdWxvcy9zaWduaWZpY2Fkby1kb3Mtc29uaG9zIj5Tb25ob3M8L2E+PC9saT48bGkgY2xhc3M9Im5hdi1pdGVtIj48YSBjbGFzcz0ibmF2LWxpbmsiICBocmVmPSIvb3JhY3Vsb3Mvc2ltcGF0aWFzIj5TaW1wYXRpYXM8L2E+PC9saT48bGkgY2xhc3M9Im5hdi1pdGVtIj48YSBjbGFzcz0ibmF2LWxpbmsiICBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici91bHRpbWFzLW5vdGljaWFzIj5Ob3TDrWNpYXM8L2E+PC9saT48bGkgY2xhc3M9Im5hdi1pdGVtIGRyb3Bkb3duIj48YSBjbGFzcz0ibmF2LWxpbmsgZHJvcGRvd24tdG9nZ2xlIiAgaHJlZj0iL29yYWN1bG9zLyIgZGF0YS1icy10b2dnbGU9ImRyb3Bkb3duIiByb2xlPSJidXR0b24iIGFyaWEtZXhwYW5kZWQ9ImZhbHNlIj5PcsOhY3Vsb3M8L2E+PHVsIGNsYXNzPSJkcm9wZG93bi1tZW51Ij48bGkgY2xhc3M9Im5hdi1pdGVtIj48YSBjbGFzcz0ibmF2LWxpbmsiICBocmVmPSIvb3JhY3Vsb3MvY29tYmluYWNhby1kZS1ub21lcyI+Q29tYmluYcOnw6NvIGRlIE5vbWVzPC9hPjwvbGk+PGxpIGNsYXNzPSJuYXYtaXRlbSI+PGEgY2xhc3M9Im5hdi1saW5rIiAgaHJlZj0iL29yYWN1bG9zL2NvbWJpbmFjYW8tZGUtc2lnbm9zIj5Db21iaW5hw6fDo28gZGUgU2lnbm9zPC9hPjwvbGk+PGxpIGNsYXNzPSJuYXYtaXRlbSI+PGEgY2xhc3M9Im5hdi1saW5rIiAgaHJlZj0iL29yYWN1bG9zL251bWVyb2xvZ2lhLWRvLW5vbWUiPk51bWVyb2xvZ2lhIGRvIE5vbWU8L2E+PC9saT48bGkgY2xhc3M9Im5hdi1pdGVtIj48YSBjbGFzcz0ibmF2LWxpbmsiICBocmVmPSIvb3JhY3Vsb3MvbnVtZXJvbG9naWEtZG8tbmFzY2ltZW50byI+TnVtZXJvbG9naWEgZG8gTmFzY2ltZW50bzwvYT48L2xpPjxsaSBjbGFzcz0ibmF2LWl0ZW0iPjxhIGNsYXNzPSJuYXYtbGluayIgIGhyZWY9Ii9vcmFjdWxvcy90YXJvdC1vbmxpbmUiPlRhcm90IE9ubGluZTwvYT48L2xpPjwvdWw+PC9saT48L3VsPiANCiAgICAgICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICA8L25hdj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sLTQgY29sLW1kLTQgcHgtMCI+DQogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic29jaWFsLWljb25zIj4NCiAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzPSJsaXN0LWdyb3VwIGxpc3QtZ3JvdXAtaG9yaXpvbnRhbCI+DQogICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Imxpc3QtZ3JvdXAtaXRlbSI+DQoNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJkcm9wZG93biI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGNsYXNzPSJkcm9wZG93bi10b2dnbGUiIGhyZWY9IiMiIHJvbGU9ImJ1dHRvbiIgZGF0YS1icy10b2dnbGU9ImRyb3Bkb3duIiBhcmlhLWV4cGFuZGVkPSJmYWxzZSIgYXJpYS1sYWJlbD0iU2VsZWNpb25lIG8gcGHDrXMiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9nbG9iZS5zdmciIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYWx0PSJTZWxlY2lvbmUgbyBwYcOtcyIgLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzcz0iZHJvcGRvd24tbWVudSBjb3VudHJ5Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YSBjbGFzcz0iZHJvcGRvd24taXRlbSIgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20ubXgiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ljb25zL2ZsYWdzL21leGljby5zdmciIGFsdD0ibWV4aWNvIiAvPiAgICANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTcOpeGljbw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgY2xhc3M9ImRyb3Bkb3duLWl0ZW0iIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmNvIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9mbGFncy9jb2xvbWJpYS5zdmciIGFsdD0iY29sb21iaWEiIC8+ICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDb2zDtG1iaWENCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGNsYXNzPSJkcm9wZG93bi1pdGVtIiBocmVmPSJodHRwczovL2pvYW9iaWR1LmNsIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9mbGFncy9jaGlsZS5zdmciIGFsdD0iY2hpbGUiIC8+ICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBDaGlsZQ0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgY2xhc3M9ImRyb3Bkb3duLWl0ZW0iIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9mbGFncy9lc3RhZG9zLXVuaWRvcy5zdmciIGFsdD0iZXN0YWRvcyB1bmlkb3MiIC8+ICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBFc3RhZG9zIFVuaWRvcw0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgY2xhc3M9ImRyb3Bkb3duLWl0ZW0iIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUucHQiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ljb25zL2ZsYWdzL3BvcnR1Z2FsLnN2ZyIgYWx0PSJwb3J0dWdhbCIgLz4gICAgDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFBvcnR1Z2FsDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YSBjbGFzcz0iZHJvcGRvd24taXRlbSIgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5lcyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvZmxhZ3MvZXNwYW5oYS5zdmciIGFsdD0iZXNwYW5oYSIgLz4gICAgDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEVzcGFuaGENCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3VsPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIGQtbm9uZSBkLW1kLWJsb2NrIHB4LTEiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vd3d3LmZhY2Vib29rLmNvbS9Kb2FvQmlkdS8iIHRhcmdldD0iX2JsYW5rIiBhcmlhLWxhYmVsPSJmYWNlYm9vayBKb8OjbyBCaWR1Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ljb25zL3NvY2lhbC9mYWNlYm9vay5zdmciIGFsdD0iZmFjZWJvb2siIC8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIGQtbm9uZSBkLW1kLWJsb2NrIHB4LTEiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vd3d3Lmluc3RhZ3JhbS5jb20vam9hb2JpZHUub2ZpY2lhbC8iIHRhcmdldD0iX2JsYW5rIiBhcmlhLWxhYmVsPSJpbnN0YWdyYW0gSm/Do28gQmlkdSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9zb2NpYWwvaW5zdGFncmFtLnN2ZyIgYWx0PSJpbnN0YWdyYW0iIC8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIGQtbm9uZSBkLW1kLWJsb2NrIHB4LTEiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vYnIucGludGVyZXN0LmNvbS9vZmljaWFsam9hb2JpZHUvIiB0YXJnZXQ9Il9ibGFuayIgYXJpYS1sYWJlbD0icGludGVyZXN0IEpvw6NvIEJpZHUiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvc29jaWFsL3BpbnRlcmVzdC5zdmciIGFsdD0icGludGVyZXN0IiAvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT4NCiAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Imxpc3QtZ3JvdXAtaXRlbSBkLW5vbmUgZC1tZC1ibG9jayBweC0xIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YSBocmVmPSIjIiBhcmlhLWxhYmVsPSJ0aWt0b2sgSm/Do28gQmlkdSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9zb2NpYWwvdGlrdG9rLnN2ZyIgYWx0PSJ0aWt0b2siIC8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIGQtbm9uZSBkLW1kLWJsb2NrIHB4LTEiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3VzZXIvdmlkZW9zam9hb2JpZHUiIHRhcmdldD0iX2JsYWNrIiBhcmlhLWxhYmVsPSJ5b3V0dWJlIEpvw6NvIEJpZHUiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvc29jaWFsL3lvdXR1YmUuc3ZnIiBhbHQ9InlvdXR1YmUiIC8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIGQtbm9uZSBkLW1kLWJsb2NrIHB4LTEiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiIGFyaWEtbGFiZWw9IndoYXRzYXBwIEpvw6NvIEJpZHUiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvc29jaWFsL3doYXRzYXBwLnN2ZyIgYWx0PSJ3aGF0c2FwcCIgLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiIGFyaWEtbGFiZWw9InNlYXJjaCI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9zZWFyY2gtbWQuc3ZnIiBhbHQ9InNlYXJjaCIgIC8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgPC91bD4NCiAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L2Rpdj4NCiAgICA8L2Rpdj4NCjwvaGVhZGVyPjxtYWluIGNsYXNzPSJwYWdlLWhvcm9zY29wZSI+DQoJPGRpdiBjbGFzcz0iY29udGFpbmVyLW1kIG1iLTMgcHktMyBweC0wIj4NCiAgICAgICAgDQogICAgICAgIDxkaXYgY2xhc3M9ImFkcyBiaWxsYm9hcmQiIHN0eWxlPSJoZWlnaHQ6MTYwcHg7Ij4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImFkcy10aXRsZSI+UHVibGljaWRhZGU8L2Rpdj4NCiAgICAgICAgPC9kaXY+DQoNCiAgICAgICAgPGRpdiBjbGFzcz0ibXktMiBweC0zIj4NCiAgICAgICAgICAgIDxuYXYgYXJpYS1sYWJlbD0iYnJlYWRjcnVtYiI+DQogICAgICAgICAgICAgICAgPG9sIGNsYXNzPSJicmVhZGNydW1iIG0tMCI+DQogICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0iYnJlYWRjcnVtYi1pdGVtIj48YSBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5iciI+SG9tZTwvYT48L2xpPg0KICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9ImJyZWFkY3J1bWItaXRlbSI+PGEgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS8iPi4uLjwvYT48L2xpPg0KICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9ImJyZWFkY3J1bWItaXRlbSBhY3RpdmUiIGFyaWEtY3VycmVudD0icGFnZSI+DQogICAgICAgICAgICAgICAgICAgICAgICBIb3LDs3Njb3BvIGRvIGRpYSBwYXJhIFZpcmdlbSAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICA8L29sPg0KICAgICAgICAgICAgPC9uYXY+DQogICAgICAgIDwvZGl2Pg0KDQogICAgICAgIDxzZWN0aW9uPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sLTEyIG15LTIgcHktMiBjYXRlZ29yeS1iYXIiPg0KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImQtZmxleCBhbGlnbi1pdGVtcy1jZW50ZXIiPg0KICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ljb25zL3pvZGlhYy92aXJnZW0uc3ZnIiAvPg0KICAgICAgICAgICAgICAgICAgICA8aDE+SG9yw7NzY29wbyBkbyBkaWEgcGFyYSBWaXJnZW08L2gxPg0KICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJyb3cgYWxpZ24taXRlbXMtc3RhcnQgY29udGVudCI+DQogICAgICAgICAgICAgICAgPCEtLSBDT05URU5UIC0tPiANCiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtMTIgY29sLW1kLTggcC00IHB4LW1kLTMgcHktbWQtMyI+DQogICAgICAgICAgICAgICAgICAgIDxpbWcgY2xhc3M9ImltZy1mbHVpZCIgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaW1hZ2VzL3pvZGlhYy92aXJnZW0uc3ZnIiAvPg0KDQogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImFkcyBoYWxmLXBhZ2UgZC1ub25lIGQtbWQtYmxvY2siIHN0eWxlPSJoZWlnaHQ6MzEwcHg7IG1hcmdpbi10b3A6IDE1cHg7Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImFkcy10aXRsZSI+UHVibGljaWRhZGU8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQoNCiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icm93Ij4NCg0KICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sLTEyIG15LTQgY29udGVudC10ZXh0Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YXJ0aWNsZT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iYWRzIHRydXZpZFBvcyBob3Jvc2NvcG8iPjwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPkRpbmhuZWlybyAtIFN1YSBoYWJpbGlkYWRlIHBhcmEgbGlkYXIgY29tIGRpbmhlaXJvIHNlZ3VlIGVtIGFsdGEgZSBwb2RlIHN1cmdpciBhIG9wb3J0dW5pZGFkZSBkZSBwZWdhciB1bSBzZXJ2aSZjY2VkaWw7byBleHRyYSBwYXJhIGRhciB1bWEgcmVmb3ImY2NlZGlsO2FkYSBubyBzZXUgYm9sc28uIFNlcGFyZSB1bSB0ZW1waW5obyBwYXJhIG9yZ2FuaXphciBzdWFzIGNvbnRhcywgZGV0YWxoYXIgbyBvciZjY2VkaWw7YW1lbnRvIGUgcGxhbmVqYXIgYXMgcHImb2FjdXRlO3hpbWFzIGNvbXByYXMuPC9wPjxwPlRyYWJhbGhvIC0gQm9tIG1vbWVudG8gcGFyYSBuZWdvY2lhciB1bSBhdW1lbnRvIG91IGlyIGF0ciZhYWN1dGU7cyBkZSB1bWEgcHJvbW8mY2NlZGlsOyZhdGlsZGU7by48L3A+ICANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnIgY2xhc3M9ImQtbm9uZSBkLW1kLWJsb2NrIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJyb3cgYm94LXN1Z2VzdGFvIG15LTIgbXktbWQtNCBwLTIgcHktbWQtNCI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPSJzdWdlc3Rhby1sYWJlbCBtYi0yIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAg4pyoIFN1Z2VzdMOjbyBkYSBJQSBwYXJhIG8gc2lnbm8gZGUgPHN0cm9uZz5WaXJnZW08L3N0cm9uZz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtMTIgY29sLW1kLTYgbXktMiI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvYXN0cm9sb2dpYS9jb25oZWNhLW8tcHJhdG8tanVuaW5vLXF1ZS10ZW0tdHVkby1hLXZlci1jb20tc2V1LXNpZ25vLnBodG1sIiBjbGFzcz0ic3VnZXN0YW8tbGluayI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9Iml0ZW0tY2FyZCBkLWZsZXggYWxpZ24taXRlbXMtY2VudGVyIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2VzdGFvLWltZy13cmFwcGVyIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC91cGxvYWRzLzIwMjUvMDYvcHJhdG8tanVuaW5vLTMwMHgxNjkuanBnIiBsb2FkaW5nPSJsYXp5Ig0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJpbWctZmx1aWQgcm91bmRlZCIgYWx0PSJDb25oZcOnYSBvIHByYXRvIGp1bmlubyBxdWUgdGVtIHR1ZG8gYSB2ZXIgY29tIHNldSBzaWdubyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9InN1Z2VzdGFvLWNvbnRlbnQgcHMtMyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg2IGNsYXNzPSJ0aXRsZS1pdGVtLW1lZGl1bSBtYi0wIj5Db25oZcOnYSBvIHByYXRvIGp1bmlubyBxdWUgdGVtIHR1ZG8gYSB2ZXIgY29tIHNldSBzaWdubzwvaDY+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0xMiBjb2wtbWQtNiBteS0yIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YSBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9hc3Ryb2xvZ2lhL3NhaWJhLW8tcXVlLWEtc3VhLXZlbnVzLXJldmVsYS1zb2JyZS1kaW5oZWlyby1mYWNpbC1lLWFwb3N0YXMucGh0bWwiIGNsYXNzPSJzdWdlc3Rhby1saW5rIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iaXRlbS1jYXJkIGQtZmxleCBhbGlnbi1pdGVtcy1jZW50ZXIiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic3VnZXN0YW8taW1nLXdyYXBwZXIiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3VwbG9hZHMvMjAyNS8wNi9zYWliYS1vLXF1ZS1hLXN1YS12ZW51cy1yZXZlbGEtc29icmUtZGluaGVpcm8tZmFjaWwtZS1hcG9zdGFzLTMwMHgxNjkuanBnIiBsb2FkaW5nPSJsYXp5Ig0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzPSJpbWctZmx1aWQgcm91bmRlZCIgYWx0PSJTYWliYSBvIHF1ZSBhIHN1YSBWw6pudXMgcmV2ZWxhIHNvYnJlIGRpbmhlaXJvIGbDoWNpbCBlIGFwb3N0YXMiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJzdWdlc3Rhby1jb250ZW50IHBzLTMiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoNiBjbGFzcz0idGl0bGUtaXRlbS1tZWRpdW0gbWItMCI+U2FpYmEgbyBxdWUgYSBzdWEgVsOqbnVzIHJldmVsYSBzb2JyZSBkaW5oZWlybyBmw6FjaWwgZSBhcG9zdGFzPC9oNj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+QW1vciAtIE1hcyBhIGVudHJhZGEgZGUgViZlY2lyYztudXMgZW0gVG91cm8gdGFtYiZlYWN1dGU7bSBkZWl4YSBvIGFzdHJhbCBtYWlzIGRlc2NvbnRyYSZpYWN1dGU7ZG8sIGluY2x1c2l2ZSBuYSB2aWRhIGFtb3Jvc2EuIE1hbnRlciBvIGJvbSBodW1vciBzZXImYWFjdXRlOyBhIGNoYXZlIHBhcmEgYnJpbGhhciBub3MgYXNzdW50b3MgZG8gY29yYSZjY2VkaWw7JmF0aWxkZTtvLjwvcD4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImFkcyBoYWxmLXBhZ2UtMDIgZC1ibG9jayBkLW1kLW5vbmUiIHN0eWxlPSJoZWlnaHQ6MzEwcHg7IG1hcmdpbi10b3A6IDE1cHg7Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJhZHMtdGl0bGUiPlB1YmxpY2lkYWRlPC9kaXY+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+PHNwYW4gY2xhc3M9ImZ3LWJvbGRlciI+UGFscGl0ZSBkbyBkaWE6PC9zcGFuPiA1MywgNjAsIDQ0PC9wPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+PHNwYW4gY2xhc3M9ImZ3LWJvbGRlciI+Q29yIHBhcmEgbyBkaWE6PC9zcGFuPiBDUkVNRTwvcD4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYXJ0aWNsZT4NCiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KDQogICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtMTIgY29sLW1kLTggbXktMyI+ICAgICAgICAgICAgICAgICAgICAgICAgICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDx1bCBjbGFzcz0ibmF2IG5hdi1waWxscyBmbGV4LWNvbHVtbiBmbGV4LW1kLXJvdyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibmF2LWl0ZW0gdGV4dC1jZW50ZXIiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgY2xhc3M9Im5hdi1saW5rIHB5LTIgYWN0aXZlIiBhcmlhLWN1cnJlbnQ9InBhZ2UiIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1kaWEvaG9yb3Njb3BvLWRvLWRpYS1wYXJhLXZpcmdlbS8iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGhvamUNCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJuYXYtaXRlbSB0ZXh0LWNlbnRlciI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YSBjbGFzcz0ibmF2LWxpbmsgcHktMiIgDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZGUtYW1hbmhhLXBhcmEtdmlyZ2VtLyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYW1hbmjDow0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Im5hdi1pdGVtIHRleHQtY2VudGVyIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGNsYXNzPSJuYXYtbGluayBweS0yIiANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tc2VtYW5hbC9wcmV2aXNhby1zZW1hbmFsLXBhcmEtby1zaWduby1kZS12aXJnZW0vIj5zZW1hbmFsPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Im5hdi1pdGVtIHRleHQtY2VudGVyIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGNsYXNzPSJuYXYtbGluayBweS0yIiBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9jYW5hbC9wcmV2aXNvZXMtZG8tbWVzIj5tZW5zYWw8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibmF2LWl0ZW0gdGV4dC1jZW50ZXIiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgY2xhc3M9Im5hdi1saW5rIHB5LTIiIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1hbW9yL3ByZXZpc2FvLWFtb3Itc2lnbm8tZmltLWRlLXNlbWFuYS5waHRtbCI+YW1vcjwvYT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3VsPg0KDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iYWRzIGhhbGYtcGFnZS0wMyIgc3R5bGU9ImhlaWdodDozMTBweDsgbWFyZ2luLXRvcDogMTVweDsiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJhZHMtdGl0bGUiPlB1YmxpY2lkYWRlPC9kaXY+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCg0KICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sLTEyIGNvbC1tZC00IG15LTMiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWxlY3QgY2xhc3M9ImZvcm0tY29udHJvbCIgb25jaGFuZ2U9ImxvY2F0aW9uLmhyZWY9dGhpcy52YWx1ZSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9IiMiPlRyb3F1ZSBvIHNpZ25vPC9vcHRpb24+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtYXJpZXMvIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOBcmllcyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtdG91cm8vIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRvdXJvICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS1nZW1lb3MvIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEfDqm1lb3MgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1kaWEvaG9yb3Njb3BvLWRvLWRpYS1wYXJhLWNhbmNlci8iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ8OibmNlciAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtbGVhby8iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTGXDo28gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1kaWEvaG9yb3Njb3BvLWRvLWRpYS1wYXJhLXZpcmdlbS8iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVmlyZ2VtICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS1saWJyYS8iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgTGlicmEgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1kaWEvaG9yb3Njb3BvLWRvLWRpYS1wYXJhLWVzY29ycGlhby8iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRXNjb3JwacOjbyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtc2FnaXRhcmlvLyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBTYWdpdMOhcmlvICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS1jYXByaWNvcm5pby8iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2Fwcmljw7NybmlvICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L29wdGlvbj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS1hcXVhcmlvLyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBBcXXDoXJpbyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtcGVpeGVzLyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBQZWl4ZXMgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvb3B0aW9uPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zZWxlY3Q+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQoNCiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0icm93Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0xMiBteS00Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDI+TWFpcyBzb2JyZSBvIHNpZ25vIGRlIFZpcmdlbTwvaDI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzPSJsaXN0LWdyb3VwIGxpc3QtZ3JvdXAtZmx1c2giPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvaXRlbS5zdmciIC8+ICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz5TaWdubzogPC9zdHJvbmc+VmlyZ2VtPC9wPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvaXRlbS5zdmciIC8+ICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz5FbGVtZW50bzogPC9zdHJvbmc+VGVycmE8L3A+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9pdGVtLnN2ZyIgLz4gICAgDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPlJlZ2VudGU6IDwvc3Ryb25nPk1lcmPDunJpbzwvcD4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Imxpc3QtZ3JvdXAtaXRlbSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ljb25zL2l0ZW0uc3ZnIiAvPiAgICANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPjxzdHJvbmc+RmxvcjogPC9zdHJvbmc+QcOndWNlbmEsIGNyYXZvLCBqYXNtaW0gZSBiZWfDtG5pYTwvcD4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Imxpc3QtZ3JvdXAtaXRlbSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ljb25zL2l0ZW0uc3ZnIiAvPiAgICANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPjxzdHJvbmc+TWV0YWw6IDwvc3Ryb25nPkFsdW3DrW5pbzwvcD4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Imxpc3QtZ3JvdXAtaXRlbSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW1nIHNyYz0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvd3AtY29udGVudC90aGVtZXMveXctamJpbmMvYXNzZXRzL2ljb25zL2l0ZW0uc3ZnIiAvPiAgICANCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPjxzdHJvbmc+UGVkcmE6IDwvc3Ryb25nPsOBZ2F0YSwgYW1hem9uaXRhIGUgamFzcGU8L3A+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9pdGVtLnN2ZyIgLz4gICAgDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPkFtdWxldG9zOiA8L3N0cm9uZz5Fc2NhcHVsw6FyaW88L3A+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9pdGVtLnN2ZyIgLz4gICAgDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPlBlcmZ1bWU6IDwvc3Ryb25nPkZydXRhaXMgbGV2ZW1lbnRlIGPDrXRyaWNvcyBlIGZsb3JhaXMgc3VhdmVzIChxdWUgY29udMOqbSBsYXZhbmRhLCBqYXNtaW0gZSBtYcOnw6MgdmVyZGUpPC9wPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvaXRlbS5zdmciIC8+ICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz5BbmpvOiA8L3N0cm9uZz5SYWZhZWw8L3A+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9pdGVtLnN2ZyIgLz4gICAgDQogICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPk9yaXjDoTogPC9zdHJvbmc+T2JhbHVhacOqPC9wPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvaXRlbS5zdmciIC8+ICAgIA0KICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz5TYW50byBQcm90ZXRvcjogPC9zdHJvbmc+U8OjbyBSb3F1ZTwvcD4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvdWw+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0xMiBteS00Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDI+VmVqYSBhcyBwcmV2aXPDtWVzIGRvIGRpYSBwYXJhIG91dHJvcyBzaWdub3M8L2gyPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzPSJfY29udGFpbmVyLXNpZ25zIj4NCiAgICA8ZGl2IGNsYXNzPSJyb3cgcHktMyI+DQoNCiAgICAgICAgPGRpdiBjbGFzcz0iY29sLTYgY29sLW1kLTQgdGV4dC1jZW50ZXIgcHgtMCBteC0wIG15LTMiPg0KICAgICAgICAgICAgPGEgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtYXJpZXMvIj4NCiAgICAgICAgICAgICAgICA8ZGl2IGlkPSJzaWduLWFyaWVzIiBjbGFzcz0ic2lnbiBzaWduLXJlZCI+DQogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNpcmNsZSBiZy1yZWQiPg0KICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD0iNjUiIGhlaWdodD0iNjUiIHZpZXdCb3g9IjAgMCA2NSA2NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cmVjdCB4PSIxLjE4MTM3IiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgZmlsbD0iI0ZBRTlGNSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjEuMTgxMzciIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBzdHJva2U9IiNGNkQ0RUIiIHN0cm9rZS13aWR0aD0iMS40NTQ1NSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0zNC45OCAyMS44NTQ3QzM2LjYxNDggMTkuOTI5IDM4LjIzODYgMTkuMTE2NSAzOS41MjY4IDE4LjgwNjVDNDAuMTY1MyAxOC42NTI4IDQwLjcwNjkgMTguNjI2MiA0MS4xMDc0IDE4LjYzOTNDNDEuMzA3NSAxOC42NDU5IDQxLjQ3MDcgMTguNjYyMyA0MS41ODk3IDE4LjY3N0M0MS42MzY3IDE4LjY4MjggNDEuNjc4NCAxOC42ODg1IDQxLjcwNzYgMTguNjkyNUw0MS43MjU1IDE4LjY5NUw0MS43MzE4IDE4LjY5NThDNDIuNjkxMiAxOC43MDY5IDQzLjc5NTMgMTkuMTQ2NCA0NC42NjAyIDE5Ljg1OTVDNDUuNTQ3MSAyMC41OTA4IDQ2LjI3MjQgMjEuNjg3MyA0Ni4yNzI0IDIzLjA1MUM0Ni4yNzI0IDI0LjIxNzYgNDYuMDY3MiAyNS4yODQ4IDQ1LjQ3NzQgMjYuMTc0MUM0NC44ODA1IDI3LjA3NCA0My45NTExIDI3LjcwMDMgNDIuNjgyOCAyOC4wOTg2QzQyLjI5OTYgMjguMjE5IDQxLjg5MTQgMjguMDA1OSA0MS43NzEgMjcuNjIyN0M0MS42NTA3IDI3LjIzOTUgNDEuODYzNyAyNi44MzEzIDQyLjI0NjkgMjYuNzEwOUM0My4yODg4IDI2LjM4MzcgNDMuODk5NSAyNS45MjE2IDQ0LjI2NTIgMjUuMzcwMUM0NC42Mzc5IDI0LjgwODIgNDQuODE3OCAyNC4wNjE0IDQ0LjgxNzggMjMuMDUxQzQ0LjgxNzggMjIuMjM3OCA0NC4zODggMjEuNTIwMyA0My43MzQ4IDIwLjk4MThDNDMuMDcwNyAyMC40MzQyIDQyLjI2NyAyMC4xNTAyIDQxLjY5NDggMjAuMTUwMkM0MS42MjY0IDIwLjE1MDIgNDEuNTUwMSAyMC4xMzkyIDQxLjUzMjUgMjAuMTM2N0w0MS41Mjk1IDIwLjEzNjNMNDEuNTA1NiAyMC4xMzNDNDEuNDc2OSAyMC4xMjkgNDEuNDQ3NiAyMC4xMjUgNDEuNDExOCAyMC4xMjA2QzQxLjMyMzggMjAuMTA5OCA0MS4yMDU1IDIwLjA5NzkgNDEuMDU5NyAyMC4wOTMxQzQwLjc2ODIgMjAuMDgzNSA0MC4zNTkzIDIwLjEwMjIgMzkuODY3MSAyMC4yMjA3QzM4Ljg5MzUgMjAuNDU0OSAzNy41MzQyIDIxLjA5MzYgMzYuMDg4OCAyMi43OTYxQzM1LjA4NzEgMjMuOTc2IDM0LjM1NDUgMjUuNTY3OCAzMy44NzI5IDI3LjEyOTVDMzMuMzkxMiAyOC42OTExIDMzLjE4MTUgMzAuMTUzNyAzMy4xODE1IDMxLjAzMjlWNDUuNTQ1NUMzMy4xODE1IDQ1Ljk0NzEgMzIuODU1OSA0Ni4yNzI3IDMyLjQ1NDIgNDYuMjcyN0MzMi4wNTI1IDQ2LjI3MjcgMzEuNzI2OSA0NS45NDcxIDMxLjcyNjkgNDUuNTQ1NVYzMUMzMS43MjY5IDMwLjExMzQgMzEuNTI3OSAyOC42NDM0IDMxLjA3MjMgMjcuMDc1OEMzMC42MTY0IDI1LjUwNzEgMjkuOTIzMyAyMy45MDk3IDI4Ljk3NzIgMjIuNzI3MUMyNy42MTI4IDIxLjAyMTYgMjYuMzM2MSAyMC4zODk2IDI1LjQzMzkgMjAuMTU5MkMyNC45NzY5IDIwLjA0MjUgMjQuNTk4NCAyMC4wMjQzIDI0LjMyOTUgMjAuMDMzN0MyNC4xOTUgMjAuMDM4MyAyNC4wODU5IDIwLjA1IDI0LjAwNDUgMjAuMDYwNkMyMy45NzEzIDIwLjA2NSAyMy45NDQzIDIwLjA2ODkgMjMuOTE3NCAyMC4wNzI5TDIzLjg5NDQgMjAuMDc2MkwyMy44OTI1IDIwLjA3NjVDMjMuODc4MyAyMC4wNzg3IDIzLjc5ODkgMjAuMDkwOSAyMy43MjY5IDIwLjA5MDlDMjMuMjEwNyAyMC4wOTA5IDIyLjQ2NTggMjAuMzY1NCAyMS44NDIyIDIwLjkxMUMyMS4yMzI0IDIxLjQ0NDYgMjAuODE3OCAyMi4xNjcxIDIwLjgxNzggMjNDMjAuODE3OCAyNC4wMTkgMjAuOTg5MiAyNC43NzQ3IDIxLjM0MzYgMjUuMzQxOEMyMS42ODg4IDI1Ljg5NDIgMjIuMjU5OCAyNi4zNTA0IDIzLjIyOTYgMjYuNjczN0MyMy42MTA3IDI2LjgwMDcgMjMuODE2NiAyNy4yMTI2IDIzLjY4OTYgMjcuNTkzNkMyMy41NjI2IDI3Ljk3NDcgMjMuMTUwNyAyOC4xODA2IDIyLjc2OTcgMjguMDUzNkMyMS41NTc3IDI3LjY0OTYgMjAuNjc0MSAyNy4wMTUgMjAuMTEwMiAyNi4xMTI3QzE5LjU1NTYgMjUuMjI1MyAxOS4zNjMzIDI0LjE2MjkgMTkuMzYzMyAyM0MxOS4zNjMzIDIxLjY1MTEgMjAuMDM5NiAyMC41NTU1IDIwLjg4NDQgMTkuODE2M0MyMS43MDMzIDE5LjA5OTggMjIuNzU3NCAxOC42NDk2IDIzLjY4NTkgMTguNjM2N0wyNS43OTM4IDE4Ljc0OTlDMjcuMDI3NiAxOS4wNjUgMjguNTY4MyAxOS44ODc1IDMwLjExMyAyMS44MTg0QzMxLjIyMjkgMjMuMjA1OCAzMS45ODQ0IDI1LjAwMjQgMzIuNDY5IDI2LjY2OTlDMzIuNDcyOCAyNi42ODMgMzIuNDc2NiAyNi42OTYgMzIuNDgwNCAyNi43MDkxQzMyLjQ4MTIgMjYuNzA2MyAzMi40ODIxIDI2LjcwMzYgMzIuNDgzIDI2LjcwMDhDMzIuOTk3MiAyNS4wMzM1IDMzLjgwNDggMjMuMjM5IDM0Ljk4IDIxLjg1NDdaIiBmaWxsPSIjQzYzQzhBIi8+DQogICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz4gDQogICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICA8cCBjbGFzcz0ibS0wIGZ3LWJvbGQiPsOBcmllczwvcD4NCiAgICAgICAgICAgICAgICAgICAgPHNwYW4+MjEvMDMgYSAyMC8wNDwvc3Bhbj4NCiAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvYT4NCiAgICAgICAgPC9kaXY+DQogICAgICAgIDxkaXYgY2xhc3M9ImNvbC02IGNvbC1tZC00IHRleHQtY2VudGVyIHB4LTAgbXgtMCBteS0zIj4NCiAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1kaWEvaG9yb3Njb3BvLWRvLWRpYS1wYXJhLXRvdXJvLyI+DQogICAgICAgICAgICAgICAgPGRpdiBpZD0ic2lnbi10YXVydXMiIGNsYXNzPSJzaWduIHNpZ24tYmx1ZSI+DQogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNpcmNsZSBiZy1ibHVlIj4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9IjY0IiBoZWlnaHQ9IjY1IiB2aWV3Qm94PSIwIDAgNjQgNjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgeD0iMC43MjcyNzMiIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBmaWxsPSIjRDdGNUY2Ii8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgeD0iMC43MjcyNzMiIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBzdHJva2U9IiNCM0VBRUUiIHN0cm9rZS13aWR0aD0iMS40NTQ1NSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTguMTgyNiAyMC44MTgyQzE4LjE4MjYgMjAuNDE2NiAxOC41MDgyIDIwLjA5MDkgMTguOTA5OSAyMC4wOTA5SDE5LjQ2N0MyMS42MjU0IDIwLjA5MDkgMjMuNzE5NiAyMC44MjU1IDI1LjQwNTEgMjIuMTczOUMyNi4yODczIDIyLjg3OTcgMjcuMDM2NiAyMy43MzcxIDI3LjYxNzkgMjQuNzA1OUwyOC4yNDg3IDI1Ljc1NzNDMjguNzY3NCAyNi42MjE4IDI5LjE5IDI3LjU0MDUgMjkuNTA4OCAyOC40OTdMMjkuNzI1MyAyOS4xNDY1QzMwLjQ0NjUgMjguOTMyOSAzMS4yMTAzIDI4LjgxODIgMzIuMDAwOCAyOC44MTgyQzMyLjc5MTMgMjguODE4MiAzMy41NTUgMjguOTMyOSAzNC4yNzYzIDI5LjE0NjVMMzQuNDkyOCAyOC40OTdDMzQuODExNiAyNy41NDA1IDM1LjIzNDIgMjYuNjIxOCAzNS43NTI5IDI1Ljc1NzNMMzYuMzgzNyAyNC43MDU5QzM2Ljk2NSAyMy43MzcxIDM3LjcxNDMgMjIuODc5NyAzOC41OTY1IDIyLjE3MzlDNDAuMjgyIDIwLjgyNTUgNDIuMzc2MiAyMC4wOTA5IDQ0LjUzNDYgMjAuMDkwOUg0NS4wOTE3QzQ1LjQ5MzQgMjAuMDkwOSA0NS44MTkgMjAuNDE2NiA0NS44MTkgMjAuODE4MkM0NS44MTkgMjEuMjE5OSA0NS40OTM0IDIxLjU0NTUgNDUuMDkxNyAyMS41NDU1SDQ0LjUzNDZDNDIuNzA2NCAyMS41NDU1IDQwLjkzMjcgMjIuMTY3NyAzOS41MDUyIDIzLjMwOTdDMzguNzU4IDIzLjkwNzUgMzguMTIzMyAyNC42MzM3IDM3LjYzMDkgMjUuNDU0M0wzNy4wMDAxIDI2LjUwNTZDMzYuNTM2IDI3LjI3OTEgMzYuMTU3OSAyOC4xMDExIDM1Ljg3MjcgMjguOTU2OUwzNS42Mjk1IDI5LjY4NjZDMzguMjI0IDMxLjAwOTMgNDAuMDAwOCAzMy43MDYzIDQwLjAwMDggMzYuODE4MkM0MC4wMDA4IDQxLjIzNjUgMzYuNDE5MSA0NC44MTgyIDMyLjAwMDggNDQuODE4MkMyNy41ODI1IDQ0LjgxODIgMjQuMDAwOCA0MS4yMzY1IDI0LjAwMDggMzYuODE4MkMyNC4wMDA4IDMzLjcwNjMgMjUuNzc3NiAzMS4wMDkzIDI4LjM3MjEgMjkuNjg2NkwyOC4xMjg5IDI4Ljk1NjlDMjcuODQzNiAyOC4xMDExIDI3LjQ2NTYgMjcuMjc5MSAyNy4wMDE1IDI2LjUwNTZMMjYuMzcwNiAyNS40NTQzQzI1Ljg3ODMgMjQuNjMzNyAyNS4yNDM2IDIzLjkwNzUgMjQuNDk2NCAyMy4zMDk3QzIzLjA2ODkgMjIuMTY3NyAyMS4yOTUyIDIxLjU0NTUgMTkuNDY3IDIxLjU0NTVIMTguOTA5OUMxOC41MDgyIDIxLjU0NTUgMTguMTgyNiAyMS4yMTk5IDE4LjE4MjYgMjAuODE4MlpNMzIuMDAwOCA0My4zNjM3QzM1LjYxNTggNDMuMzYzNyAzOC41NDYzIDQwLjQzMzIgMzguNTQ2MyAzNi44MTgyQzM4LjU0NjMgMzMuMjAzMyAzNS42MTU4IDMwLjI3MjggMzIuMDAwOCAzMC4yNzI4QzI4LjM4NTggMzAuMjcyOCAyNS40NTUzIDMzLjIwMzMgMjUuNDU1MyAzNi44MTgyQzI1LjQ1NTMgNDAuNDMzMiAyOC4zODU4IDQzLjM2MzcgMzIuMDAwOCA0My4zNjM3WiIgZmlsbD0iIzI4QTJCMiIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+DQogICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICA8cCBjbGFzcz0ibS0wIGZ3LWJvbGQiPlRvdXJvPC9wPg0KICAgICAgICAgICAgICAgICAgICA8c3Bhbj4yMS8wNCBhIDIwLzA1PC9zcGFuPg0KICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC9hPg0KICAgICAgICA8L2Rpdj4NCiAgICAgICAgPGRpdiBjbGFzcz0iY29sLTYgY29sLW1kLTQgdGV4dC1jZW50ZXIgcHgtMCBteC0wIG15LTMiPg0KICAgICAgICAgICAgPGEgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtZ2VtZW9zLyI+DQogICAgICAgICAgICAgICAgPGRpdiBpZD0ic2lnbi1nZW1pbmkiIGNsYXNzPSJzaWduIHNpZ24teWVsbG93Ij4NCiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY2lyY2xlIGJnLXllbGxvdyI+DQogICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NSIgdmlld0JveD0iMCAwIDY0IDY1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgZmlsbD0iI0Y0RUREMSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgc3Ryb2tlPSIjRTlENzlGIiBzdHJva2Utd2lkdGg9IjEuNDU0NTUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMTkuNzUzMSAyMC40MjM3QzE5Ljk3MDkgMjAuMDg2NiAyMC40MjA2IDE5Ljk4OTYgMjAuNzU3OSAyMC4yMDY5QzIwLjc1ODEgMjAuMjA3IDIwLjc1ODYgMjAuMjA3NCAyMC4zNjQxIDIwLjgxODNMMTkuOTY5NSAyMS40MjkyQzE5LjYzMjEgMjEuMjExMyAxOS41MzUyIDIwLjc2MTEgMTkuNzUzMSAyMC40MjM3WiIgZmlsbD0iI0NCOTQzNSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMjYuOTA5NSA0MC45NDY5VjIzLjk2MjVDMjUuNDk2OCAyMy42ODQ4IDI0LjI3MTUgMjMuMzIxMiAyMy4yNjEyIDIyLjk1N0MyMi4yMjA0IDIyLjU4MTkgMjEuNDA2MyAyMi4yMDU1IDIwLjg0ODQgMjEuOTIwNUMyMC41Njk0IDIxLjc3NzkgMjAuMzU0MiAyMS42NTggMjAuMjA2NiAyMS41NzI0QzIwLjEzMjcgMjEuNTI5NSAyMC4wNzU4IDIxLjQ5NTIgMjAuMDM2MSAyMS40NzA5TDE5Ljk4OTcgMjEuNDQyMUwxOS45NzYzIDIxLjQzMzZMMTkuOTcyMSAyMS40MzA5TDE5Ljk2OTUgMjEuNDI5MkMxOS45NjkzIDIxLjQyOTEgMTkuOTY5NSAyMS40MjkyIDIwLjM2NDEgMjAuODE4M0wyMC43NTc5IDIwLjIwNjlMMjAuNzY0OCAyMC4yMTEyTDIwLjc5NjcgMjAuMjMxMUMyMC44MjY1IDIwLjI0OTMgMjAuODczMyAyMC4yNzc1IDIwLjkzNjUgMjAuMzE0MkMyMS4wNjMxIDIwLjM4NzcgMjEuMjU1NSAyMC40OTUxIDIxLjUxMDMgMjAuNjI1MkMyMi4wMTk4IDIwLjg4NTYgMjIuNzc3NiAyMS4yMzY2IDIzLjc1NDQgMjEuNTg4N0MyNS43MDg1IDIyLjI5MyAyOC41MzM2IDIzLjAwMDEgMzIuMDAwNCAyMy4wMDAxQzM1LjQ2NzIgMjMuMDAwMSAzOC4yOTI0IDIyLjI5MyA0MC4yNDY1IDIxLjU4ODdDNDEuMjIzMyAyMS4yMzY2IDQxLjk4MTEgMjAuODg1NiA0Mi40OTA2IDIwLjYyNTJDNDIuNzQ1MyAyMC40OTUxIDQyLjkzNzggMjAuMzg3NyA0My4wNjQzIDIwLjMxNDJDNDMuMTI3NiAyMC4yNzc1IDQzLjE3NDQgMjAuMjQ5MyA0My4yMDQyIDIwLjIzMTFMNDMuMjM2MSAyMC4yMTEyTDQzLjI0MjIgMjAuMjA3NEM0My41Nzk2IDE5Ljk4OTggNDQuMDI5OSAyMC4wODY1IDQ0LjI0NzcgMjAuNDIzN0M0NC40NjU3IDIwLjc2MTEgNDQuMzY4OCAyMS4yMTEzIDQ0LjAzMTQgMjEuNDI5Mkw0My42MzY4IDIwLjgxODNDNDQuMDMxNCAyMS40MjkyIDQ0LjAzMTYgMjEuNDI5MSA0NC4wMzE0IDIxLjQyOTJMNDQuMDI4OCAyMS40MzA5TDQ0LjAyNDYgMjEuNDMzNkw0NC4wMTEyIDIxLjQ0MjFMNDMuOTY0OCAyMS40NzA5QzQzLjkyNTEgMjEuNDk1MiA0My44NjgxIDIxLjUyOTUgNDMuNzk0MyAyMS41NzI0QzQzLjY0NjcgMjEuNjU4IDQzLjQzMTUgMjEuNzc3OSA0My4xNTI1IDIxLjkyMDVDNDIuNTk0NiAyMi4yMDU1IDQxLjc4MDUgMjIuNTgxOSA0MC43Mzk3IDIyLjk1N0MzOS43Mjk0IDIzLjMyMTIgMzguNTA0MSAyMy42ODQ4IDM3LjA5MTQgMjMuOTYyNVY0MC45NDY5QzM4LjUwNDEgNDEuMjI0NiAzOS43Mjk0IDQxLjU4ODEgNDAuNzM5NyA0MS45NTIzQzQxLjc4MDUgNDIuMzI3NSA0Mi41OTQ2IDQyLjcwMzggNDMuMTUyNSA0Mi45ODg5QzQzLjQzMTUgNDMuMTMxNCA0My42NDY3IDQzLjI1MTMgNDMuNzk0MyA0My4zMzdDNDMuODY4MSA0My4zNzk4IDQzLjkyNTEgNDMuNDE0MSA0My45NjQ4IDQzLjQzODRMNDQuMDExMiA0My40NjczTDQ0LjAyNDYgNDMuNDc1OEw0NC4wMjg4IDQzLjQ3ODRMNDQuMDMwMyA0My40Nzk0QzQ0LjAzMDYgNDMuNDc5NiA0NC4wMzE0IDQzLjQ4MDEgNDMuNjM2OCA0NC4wOTFMNDQuMDMwMyA0My40Nzk0QzQ0LjM2NzcgNDMuNjk3MyA0NC40NjU3IDQ0LjE0ODIgNDQuMjQ3NyA0NC40ODU2QzQ0LjAyOTkgNDQuODIyOSA0My41OCA0NC45MTk4IDQzLjI0MjYgNDQuNzAyMkw0My4yMzYxIDQ0LjY5ODFMNDMuMjA0MiA0NC42NzgzQzQzLjE3NDQgNDQuNjYgNDMuMTI3NiA0NC42MzE4IDQzLjA2NDMgNDQuNTk1MUM0Mi45Mzc4IDQ0LjUyMTcgNDIuNzQ1MyA0NC40MTQzIDQyLjQ5MDYgNDQuMjg0MUM0MS45ODExIDQ0LjAyMzcgNDEuMjIzMyA0My42NzI4IDQwLjI0NjUgNDMuMzIwN0MzOC4yOTI0IDQyLjYxNjMgMzUuNDY3MiA0MS45MDkyIDMyLjAwMDQgNDEuOTA5MkMyOC41MzM2IDQxLjkwOTIgMjUuNzA4NSA0Mi42MTYzIDIzLjc1NDQgNDMuMzIwN0MyMi43Nzc2IDQzLjY3MjggMjIuMDE5OCA0NC4wMjM3IDIxLjUxMDMgNDQuMjg0MUMyMS4yNTU1IDQ0LjQxNDMgMjEuMDYzMSA0NC41MjE3IDIwLjkzNjUgNDQuNTk1MUMyMC44NzMzIDQ0LjYzMTggMjAuODI2NSA0NC42NiAyMC43OTY3IDQ0LjY3ODNMMjAuNzY0OCA0NC42OTgxTDIwLjc1ODYgNDQuNzAyTDIwLjM2NDEgNDQuMDkxQzE5Ljk2OTUgNDMuNDgwMSAxOS45Njk3IDQzLjQ3OTkgMTkuOTcgNDMuNDc5OEwxOS45NzIxIDQzLjQ3ODRMMTkuOTc2MyA0My40NzU4TDE5Ljk4OTcgNDMuNDY3M0wyMC4wMzYxIDQzLjQzODRDMjAuMDc1OCA0My40MTQxIDIwLjEzMjcgNDMuMzc5OCAyMC4yMDY2IDQzLjMzN0MyMC4zNTQyIDQzLjI1MTMgMjAuNTY5NCA0My4xMzE0IDIwLjg0ODQgNDIuOTg4OUMyMS40MDYzIDQyLjcwMzggMjIuMjIwNCA0Mi4zMjc1IDIzLjI2MTIgNDEuOTUyM0MyNC4yNzE1IDQxLjU4ODEgMjUuNDk2OCA0MS4yMjQ2IDI2LjkwOTUgNDAuOTQ2OVpNMjguMzY0MSA0MC43MDM2QzI5LjQ4NDUgNDAuNTQ4NyAzMC43MDAyIDQwLjQ1NDcgMzIuMDAwNCA0MC40NTQ3QzMzLjMwMDcgNDAuNDU0NyAzNC41MTY0IDQwLjU0ODcgMzUuNjM2OCA0MC43MDM2VjI0LjIwNTdDMzQuNTE2NCAyNC4zNjA2IDMzLjMwMDcgMjQuNDU0NyAzMi4wMDA0IDI0LjQ1NDdDMzAuNzAwMiAyNC40NTQ3IDI5LjQ4NDUgMjQuMzYwNiAyOC4zNjQxIDI0LjIwNTdWNDAuNzAzNloiIGZpbGw9IiNDQjk0MzUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMjAuNzU4NiA0NC43MDJDMjAuNzU4NSA0NC43MDIgMjAuNzU4OCA0NC43MDE5IDIwLjc1ODYgNDQuNzAyVjQ0LjcwMloiIGZpbGw9IiNDQjk0MzUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMjAuNzU4NiA0NC43MDJDMjAuNDIxMyA0NC45MTk2IDE5Ljk3MSA0NC44MjI5IDE5Ljc1MzEgNDQuNDg1NkMxOS41MzUyIDQ0LjE0ODIgMTkuNjMyNiA0My42OTc3IDE5Ljk3IDQzLjQ3OThMMjAuMzY0MSA0NC4wOTFDMjAuNzU4NiA0NC43MDIgMjAuNzU4OCA0NC43MDE4IDIwLjc1ODYgNDQuNzAyWiIgZmlsbD0iI0NCOTQzNSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+DQogICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICA8cCBjbGFzcz0ibS0wIGZ3LWJvbGQiPkfDqm1lb3M8L3A+DQogICAgICAgICAgICAgICAgICAgIDxzcGFuPjIxLzA1IGEgMjAvMDY8L3NwYW4+DQogICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2E+DQogICAgICAgIDwvZGl2Pg0KICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtNiBjb2wtbWQtNCB0ZXh0LWNlbnRlciBweC0wIG14LTAgbXktMyI+DQogICAgICAgICAgICA8YSBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS1jYW5jZXIvIj4NCiAgICAgICAgICAgICAgICA8ZGl2IGlkPSJzaWduLWNhbmNlciIgY2xhc3M9InNpZ24gc2lnbi1ncmVlbiI+DQogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNpcmNsZSBiZy1ncmVlbiI+DQogICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NSIgdmlld0JveD0iMCAwIDY0IDY1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgZmlsbD0iI0NGRkNFNSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgc3Ryb2tlPSIjQTNGN0QxIiBzdHJva2Utd2lkdGg9IjEuNDU0NTUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTMyLjcyNzQgNDUuNTQ1NUMzOC4yNzUgNDUuNTQ1NSA0My4wNTkgNDIuMjc2NCA0NS4yNTgxIDM3LjU1OTZDNDYuMDYzNCAzNi41NjE0IDQ2LjU0NTUgMzUuMjkxNSA0Ni41NDU1IDMzLjkwOTFDNDYuNTQ1NSAzMC42OTU4IDQzLjk0MDYgMjguMDkwOSA0MC43Mjc0IDI4LjA5MDlDMzcuNTE0MSAyOC4wOTA5IDM0LjkwOTIgMzAuNjk1OCAzNC45MDkyIDMzLjkwOTFDMzQuOTA5MiAzNy4xMjI0IDM3LjUxNDEgMzkuNzI3MyA0MC43Mjc0IDM5LjcyNzNDNDEuMjg3OCAzOS43MjczIDQxLjgyOTggMzkuNjQ4IDQyLjM0MjcgMzkuNTAwMUM0MC4wNzYgNDIuMzAwNSAzNi42MTA4IDQ0LjA5MDkgMzIuNzI3NCA0NC4wOTA5QzI5LjgwODggNDQuMDkwOSAyNy4xMjY0IDQzLjA3OTYgMjUuMDExNSA0MS4zODgzQzI0LjcwNTYgNDEuMDQyNSAyNC4zMjQ0IDQwLjY0MTMgMjQuMTU4OCA0MC41OEMyNC4wMTk0IDQwLjUyODUgMjMuODczNyA0MC41MDAyIDIzLjc0NiA0MC41MDAyQzIzLjM0NDMgNDAuNTAwMiAyMy4wMTg3IDQwLjgyNTggMjMuMDE4NyA0MS4yMjc1QzIzLjAxODcgNDEuNDYyMiAyMy4xMjk5IDQxLjY3MSAyMy4zMDI1IDQxLjgwNEwyMy4yNzgzIDQxLjgwOThDMjUuNzQ5NSA0NC4xMjY3IDI5LjA3MjggNDUuNTQ1NSAzMi43Mjc0IDQ1LjU0NTVaTTQ0LjAyOCAzNi43NjM2QzQ0LjY5MDMgMzUuOTk4NCA0NS4wOTEgMzUuMDAwNSA0NS4wOTEgMzMuOTA5MUM0NS4wOTEgMzEuNDk5MSA0My4xMzczIDI5LjU0NTUgNDAuNzI3NCAyOS41NDU1QzM4LjMxNzQgMjkuNTQ1NSAzNi4zNjM3IDMxLjQ5OTEgMzYuMzYzNyAzMy45MDkxQzM2LjM2MzcgMzYuMzE5MSAzOC4zMTc0IDM4LjI3MjggNDAuNzI3NCAzOC4yNzI4QzQyLjAzOTQgMzguMjcyOCA0My4yMTYxIDM3LjY5MzcgNDQuMDE2MSAzNi43NzczTDQ0LjAyMTUgMzYuNzY1MUw0NC4wMjggMzYuNzYzNloiIGZpbGw9IiMwOEMxODUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTMyLjcyNzQgMTcuOTA5MUMzOC4xNDU4IDE3LjkwOTEgNDIuODM1NyAyMS4wMjc3IDQ1LjEwMDMgMjUuNTY3OUM0NS4xMDAzIDI1LjQzNDQgNDUuMTIwNyAyNS42NTY4IDQ1LjEwMDMgMjUuNTY3OUM0NS4xMDAzIDI1Ljk2OTUgNDUuMTgwNyAyNi42MzY0IDQ0LjM2MzcgMjYuNjM2NEM0My45NjIxIDI2LjYzNjQgNDMuNjM2NSAyNS45MDkxIDQzLjYzNjUgMjUuOTA5MUM0My42MzY1IDI1LjkwNTcgNDMuNjM1NiAyNS45MDIyIDQzLjYzNCAyNS44OTkyQzQxLjU1MTEgMjIuMDA5NSAzNy40NDgzIDE5LjM2MzcgMzIuNzI3NCAxOS4zNjM3QzI3Ljk3MTggMTkuMzYzNyAyMy44NDMzIDIyLjA0ODcgMjEuNzc1MiAyNS45ODU0QzIyLjY0MDYgMjUuNDc0OCAyMy42NDk4IDI1LjE4MTggMjQuNzI3NCAyNS4xODE4QzI3Ljk0MDcgMjUuMTgxOCAzMC41NDU1IDI3Ljc4NjcgMzAuNTQ1NSAzMUMzMC41NDU1IDM0LjIxMzMgMjcuOTQwNyAzNi44MTgyIDI0LjcyNzQgMzYuODE4MkMyMS44MjcxIDM2LjgxODIgMTkuNDIyNSAzNC42OTYxIDE4Ljk4MTUgMzEuOTE5OUwxOC45MTA4IDMxLjkzNzFDMTguOTA5OCAzMS44NjczIDE4LjkwOTIgMzEuNzk3NCAxOC45MDkyIDMxLjcyNzNDMTguOTA5MiAzMS41OCAxOC45MTE2IDMxLjQzMzIgMTguOTE2MSAzMS4yODY5QzE4LjkxMTUgMzEuMTkxOSAxOC45MDkyIDMxLjA5NjIgMTguOTA5MiAzMUMxOC45MDkyIDMwLjU0NDcgMTguOTYxNSAzMC4xMDE3IDE5LjA2MDQgMjkuNjc2NUMyMC4wNTExIDIzLjAxNzUgMjUuNzkyNyAxNy45MDkxIDMyLjcyNzQgMTcuOTA5MVpNMjAuMzcyIDMxLjI3MTZDMjAuNTEyMiAzMy41NTUxIDIyLjQwODYgMzUuMzYzNyAyNC43Mjc0IDM1LjM2MzdDMjcuMTM3MyAzNS4zNjM3IDI5LjA5MSAzMy40MSAyOS4wOTEgMzFDMjkuMDkxIDI4LjU5IDI3LjEzNzMgMjYuNjM2NCAyNC43Mjc0IDI2LjYzNjRDMjIuNjc4MyAyNi42MzY0IDIwLjk1OTIgMjguMDQ4NyAyMC40OTAyIDI5Ljk1MjlDMjAuNDI4IDMwLjM4NTYgMjAuMzg4MiAzMC44MjU2IDIwLjM3MiAzMS4yNzE2WiIgZmlsbD0iIzA4QzE4NSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+DQogICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICA8cCBjbGFzcz0ibS0wIGZ3LWJvbGQiPkPDom5jZXI8L3A+DQogICAgICAgICAgICAgICAgICAgIDxzcGFuPjIxLzA2IGEgMjEvMDc8L3NwYW4+DQogICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2E+DQogICAgICAgIDwvZGl2Pg0KICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtNiBjb2wtbWQtNCB0ZXh0LWNlbnRlciBweC0wIG14LTAgbXktMyI+DQogICAgICAgICAgICA8YSBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS1sZWFvLyI+DQogICAgICAgICAgICAgICAgPGRpdiBpZD0ic2lnbi1sZW8iIGNsYXNzPSJzaWduIHNpZ24tcmVkIj4NCiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY2lyY2xlIGJnLXJlZCI+DQogICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NSIgdmlld0JveD0iMCAwIDY0IDY1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgZmlsbD0iI0ZBRTlGNSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgc3Ryb2tlPSIjRjZENEVCIiBzdHJva2Utd2lkdGg9IjEuNDU0NTUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTMxLjM3MDYgMTkuMzYzN0MyOS43MTgxIDE5LjM2MzcgMjguMTMzMyAyMC4wMjAxIDI2Ljk2NDggMjEuMTg4NkMyNi4wMTE1IDIyLjE0MTkgMjUuMzkzMiAyMy4zNzg2IDI1LjIwMjUgMjQuNzEzMkwyNS4wMDM5IDI2LjEwMzdDMjQuODEyOCAyNy40NDEzIDI0Ljg4MyAyOC44MDMyIDI1LjIxMDcgMzAuMTE0TDI1Ljg4NjEgMzIuODE1NEMyNS4zMDI4IDMyLjU4MjYgMjQuNjY2NCAzMi40NTQ2IDI0LjAwMDEgMzIuNDU0NkMyMS4xODg1IDMyLjQ1NDYgMTguOTA5MiAzNC43MzM5IDE4LjkwOTIgMzcuNTQ1NUMxOC45MDkyIDQwLjM1NzEgMjEuMTg4NSA0Mi42MzY0IDI0LjAwMDEgNDIuNjM2NEMyNi44MTE3IDQyLjYzNjQgMjkuMDkxIDQwLjM1NzEgMjkuMDkxIDM3LjU0NTVDMjkuMDkxIDM2LjE1MiAyOC41MzExIDM0Ljg4OTIgMjcuNjI0IDMzLjk2OTlDMjcuNjA3NiAzMy43NzExIDI3LjU3NSAzMy41NzM4IDI3LjUyNjUgMzMuMzhMMjYuNjIxOSAyOS43NjEyQzI2LjMzOTggMjguNjMzIDI2LjI3OTMgMjcuNDYwNyAyNi40NDM4IDI2LjMwOTRMMjYuNjQyNCAyNC45MTg5QzI2Ljc4ODYgMjMuODk1OSAyNy4yNjI2IDIyLjk0NzkgMjcuOTkzMyAyMi4yMTcxQzI4Ljg4OSAyMS4zMjE0IDMwLjEwMzkgMjAuODE4MiAzMS4zNzA2IDIwLjgxODJIMzIuMzgzMUMzMi45OTg0IDIwLjgxODIgMzMuNjA4MSAyMC45MzU2IDM0LjE3OTQgMjEuMTY0MkwzNC4zODAyIDIxLjI0NDVDMzUuNjk2MSAyMS43NzA4IDM2LjY3OTMgMjIuODk3MSAzNy4wMjMgMjQuMjcyMUMzNy41MjMgMjYuMjcyMiAzNy40NDIzIDI4LjM3MzUgMzYuNzkwNCAzMC4zMjkzTDM1LjY2NDkgMzMuNzA1NkwzNC40Njc1IDM4LjQ5NTZDMzQuMjY3NiAzOS4yOTQ5IDM0LjI0NzggNDAuMTI4NyAzNC40MDk0IDQwLjkzNjZMMzQuNDgxNSA0MS4yOTcyQzM0LjgwNTMgNDIuOTE2MyAzNS45MjUgNDQuMjYyNCAzNy40NTgxIDQ0Ljg3NTZDMzguNjIzIDQ1LjM0MTYgMzkuOTIyNiA0NS4zNDE2IDQxLjA4NzUgNDQuODc1Nkw0MS4yMjY4IDQ0LjgxOTlDNDIuNjYzNyA0NC4yNDUyIDQzLjc3NDcgNDMuMDY2OSA0NC4yNjQxIDQxLjU5ODdMNDQuMzI2NCA0MS40MTE4QzQ0LjQ1MzQgNDEuMDMwOCA0NC4yNDc1IDQwLjYxODkgNDMuODY2NCA0MC40OTE5QzQzLjQ4NTQgNDAuMzY0OSA0My4wNzM1IDQwLjU3MDggNDIuOTQ2NSA0MC45NTE5TDQyLjg4NDIgNDEuMTM4N0M0Mi41MzAxIDQyLjIwMSA0MS43MjYzIDQzLjA1MzUgNDAuNjg2NiA0My40Njk0TDQwLjU0NzMgNDMuNTI1MUMzOS43MjkyIDQzLjg1MjQgMzguODE2NSA0My44NTI0IDM3Ljk5ODMgNDMuNTI1MUMzNi45MjE2IDQzLjA5NDQgMzYuMTM1MiA0Mi4xNDkxIDM1LjkwNzggNDEuMDExOUwzNS44MzU3IDQwLjY1MTRDMzUuNzE2MyA0MC4wNTQ2IDM1LjczMSAzOS40Mzg4IDM1Ljg3ODYgMzguODQ4NEwzNy4wNjI1IDM0LjExMjZMMzguMTcwMyAzMC43ODkzQzM4LjkwOTcgMjguNTcxIDM5LjAwMTIgMjYuMTg3OCAzOC40MzQxIDIzLjkxOTRDMzcuOTc3MSAyMi4wOTEyIDM2LjY3IDIwLjU5MzggMzQuOTIwNCAxOS44OTRMMzQuNzE5NiAxOS44MTM3QzMzLjk3NjUgMTkuNTE2NCAzMy4xODM1IDE5LjM2MzcgMzIuMzgzMSAxOS4zNjM3SDMxLjM3MDZaTTI3LjgxODMgMzcuNTQ1NUMyNy44MTgzIDM5LjY1NDIgMjYuMTA4OCA0MS4zNjM3IDI0LjAwMDEgNDEuMzYzN0MyMS44OTE0IDQxLjM2MzcgMjAuMTgxOSAzOS42NTQyIDIwLjE4MTkgMzcuNTQ1NUMyMC4xODE5IDM1LjQzNjggMjEuODkxNCAzMy43MjczIDI0LjAwMDEgMzMuNzI3M0MyNi4xMDg4IDMzLjcyNzMgMjcuODE4MyAzNS40MzY4IDI3LjgxODMgMzcuNTQ1NVoiIGZpbGw9IiNDNjNDOEEiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPiANCiAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzPSJtLTAgZnctYm9sZCI+TGXDo288L3A+DQogICAgICAgICAgICAgICAgICAgIDxzcGFuPjIyLzA3IGEgMjIvMDg8L3NwYW4+DQogICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2E+DQogICAgICAgIDwvZGl2Pg0KICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtNiBjb2wtbWQtNCB0ZXh0LWNlbnRlciBweC0wIG14LTAgbXktMyI+DQogICAgICAgICAgICA8YSBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS12aXJnZW0vIj4NCiAgICAgICAgICAgICAgICA8ZGl2IGlkPSJzaWduLXZpcmdvIiBjbGFzcz0ic2lnbiBzaWduLWJsdWUiPg0KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjaXJjbGUgYmctYmx1ZSI+DQogICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NSIgdmlld0JveD0iMCAwIDY0IDY1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgZmlsbD0iI0Q3RjVGNiIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgc3Ryb2tlPSIjQjNFQUVFIiBzdHJva2Utd2lkdGg9IjEuNDU0NTUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTE4LjQ1NTYgMjAuMjUwM0MxOC43NjkyIDE5Ljk5OTQgMTkuMjI2OSAyMC4wNTAzIDE5LjQ3NzggMjAuMzYzOUwyMS44MzIyIDIzLjMwNjlMMjMuNzkxMiAyMC45NzA1QzI0LjY2NDkgMTkuOTI4NSAyNi4yNjg3IDE5LjkzMDkgMjcuMTM5MiAyMC45NzU2TDI5LjA5MTcgMjMuMzE4NkwzMS4wNTIgMjAuOTY2M0MzMS45MjQzIDE5LjkxOTUgMzMuNTMxOSAxOS45MTk1IDM0LjQwNDIgMjAuOTY2M0wzNi41ODYgMjMuNTg0NUMzNi45MTI4IDIzLjk3NjYgMzcuMDkxNyAyNC40NzA4IDM3LjA5MTcgMjQuOTgxMlYyNi4zNDk5QzM3LjU3ODQgMjUuODgzNCAzOC4wMDAzIDI1LjUwOTMgMzguMzk2MyAyNS4yMjY0QzM5LjE0NjIgMjQuNjkwOCAzOS44Mzc2IDI0LjQ1NDYgNDAuNzI4MSAyNC40NTQ2QzQxLjMwMzMgMjQuNDU0NiA0MS45NTYgMjQuNzI2MSA0Mi41MzY0IDI1LjE3NDZDNDMuMTMzOCAyNS42MzYyIDQzLjcwODQgMjYuMzI0MiA0NC4xMiAyNy4yNTAxQzQ0Ljk0ODUgMjkuMTE0NCA0NS4wOTAyIDMxLjg3MTMgNDMuNTg1MiAzNS42MzM4QzQyLjQ4NjkgMzguMzc5NiA0MC43MzggNDAuMjExMyAzOC45MDQxIDQxLjM5OTFDMzkuNjcxOCA0Mi4wMDIxIDQwLjc1MDUgNDIuNDcyOCA0Mi4yNjI5IDQyLjY0MDlDNDIuNjYyMiA0Mi42ODUyIDQyLjk0OTggNDMuMDQ0OCA0Mi45MDU1IDQzLjQ0NEM0Mi44NjExIDQzLjg0MzIgNDIuNTAxNSA0NC4xMzA5IDQyLjEwMjMgNDQuMDg2NUM0MC4wNDcxIDQzLjg1ODIgMzguNTc1NyA0My4xMjYgMzcuNTU4IDQyLjE1NTlDMzUuNzAxOCA0My4wNTMzIDMzLjg5ODkgNDMuMzYzNyAzMi43MjgxIDQzLjM2MzdDMzIuMzI2NCA0My4zNjM3IDMyLjAwMDggNDMuMDM4MSAzMi4wMDA4IDQyLjYzNjRDMzIuMDAwOCA0Mi4yMzQ4IDMyLjMyNjQgNDEuOTA5MSAzMi43MjgxIDQxLjkwOTFDMzMuNjYyMSA0MS45MDkxIDM1LjA5NjIgNDEuNjcxMiAzNi42MDgzIDQwLjk5NTFDMzYuNjA2NiA0MC45OTI0IDM2LjYwNDkgNDAuOTg5NyAzNi42MDMyIDQwLjk4N0MzNS42ODA1IDM5LjUxODggMzUuNTA5NiAzNy44Nzc5IDM1LjYzNzIgMzYuNzc0N1YyNC45ODEyQzM1LjYzNzIgMjQuODExMSAzNS41Nzc1IDI0LjY0NjMgMzUuNDY4NiAyNC41MTU2TDMzLjI4NjggMjEuODk3NUMzMi45OTYgMjEuNTQ4NSAzMi40NjAyIDIxLjU0ODUgMzIuMTY5NCAyMS44OTc1TDI5LjgxOSAyNC43MTc5VjM5LjcyNzNDMjkuODE5IDQwLjEyOSAyOS40OTM0IDQwLjQ1NDYgMjkuMDkxNyA0MC40NTQ2QzI4LjY5MDEgNDAuNDU0NiAyOC4zNjQ1IDQwLjEyOSAyOC4zNjQ1IDM5LjcyNzNWMjQuNzE3OUwyNi4wMjE4IDIxLjkwNjdDMjUuNzMxNiAyMS41NTg1IDI1LjE5NzEgMjEuNTU3NyAyNC45MDU4IDIxLjkwNTFMMjIuNTQ2MyAyNC43MTkyVjM5LjcyNzNDMjIuNTQ2MyA0MC4xMjkgMjIuMjIwNyA0MC40NTQ2IDIxLjgxOSA0MC40NTQ2QzIxLjQxNzMgNDAuNDU0NiAyMS4wOTE3IDQwLjEyOSAyMS4wOTE3IDM5LjcyNzNWMjQuNzA5N0wxOC4zNDIgMjEuMjcyNkMxOC4wOTExIDIwLjk1ODkgMTguMTQxOSAyMC41MDEzIDE4LjQ1NTYgMjAuMjUwM1pNMzcuMDkxNyAzNi44Mjc1QzM3LjA5MTggMzYuODIwNyAzNy4wOTE4IDM2LjgxMzkgMzcuMDkxNyAzNi44MDcyVjI4LjM5MjNDMzguMDc3OSAyNy40MDcxIDM4LjcwNDIgMjYuNzk0IDM5LjI0MTcgMjYuNDFDMzkuNzY0NiAyNi4wMzY2IDQwLjE2NCAyNS45MDkyIDQwLjcyODEgMjUuOTA5MkM0MC44ODAyIDI1LjkwOTIgNDEuMjI3NSAyNi4wMDEzIDQxLjY0NyAyNi4zMjU1QzQyLjA0OTcgMjYuNjM2NyA0Mi40NzUxIDI3LjEzMDUgNDIuNzkwOCAyNy44NDA5QzQzLjQxNjggMjkuMjQ5NCA0My42Mzg3IDMxLjU4MzQgNDIuMjM0NyAzNS4wOTM2QzQxLjIyMzEgMzcuNjIyNCAzOS41ODY0IDM5LjI3MTMgMzcuODk5MSA0MC4zMTI2QzM3Ljg3NzEgNDAuMjc5NSAzNy44NTU3IDQwLjI0NjMgMzcuODM0OCA0MC4yMTMxQzM3LjI3NDggMzkuMzIyIDM3LjA3MDggMzguMzM0MiAzNy4wNTUyIDM3LjU0NTVIMzcuMDkxN1YzNi44Mjc1WiIgZmlsbD0iIzI4QTJCMiIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+DQogICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICA8cCBjbGFzcz0ibS0wIGZ3LWJvbGQiPlZpcmdlbTwvcD4NCiAgICAgICAgICAgICAgICAgICAgPHNwYW4+MjMvMDggYSAyMi8wOTwvc3Bhbj4NCiAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvYT4NCiAgICAgICAgPC9kaXY+DQogICAgICAgIDxkaXYgY2xhc3M9ImNvbC02IGNvbC1tZC00IHRleHQtY2VudGVyIHB4LTAgbXgtMCBteS0zIj4NCiAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1kaWEvaG9yb3Njb3BvLWRvLWRpYS1wYXJhLWxpYnJhLyI+DQogICAgICAgICAgICAgICAgPGRpdiBpZD0ic2lnbi1saWJyYSIgY2xhc3M9InNpZ24gc2lnbi15ZWxsb3ciPg0KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjaXJjbGUgYmcteWVsbG93Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9IjY0IiBoZWlnaHQ9IjY1IiB2aWV3Qm94PSIwIDAgNjQgNjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgeD0iMC43MjcyNzMiIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBmaWxsPSIjRjRFREQxIi8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgeD0iMC43MjcyNzMiIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBzdHJva2U9IiNFOUQ3OUYiIHN0cm9rZS13aWR0aD0iMS40NTQ1NSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0zNy4wOTEzIDM2LjgxODJDMzcuMDkxMyAzNi40MTY2IDM3LjQxNjkgMzYuMDkxIDM3LjgxODUgMzYuMDkxSDM3Ljc3MzFDMzkuNTg0OSAzNC40OTE4IDQwLjcyNzYgMzIuMTUyMSA0MC43Mjc2IDI5LjU0NTVDNDAuNzI3NiAyNC43MjU2IDM2LjgyMDMgMjAuODE4MiAzMi4wMDA0IDIwLjgxODJDMjcuMTgwNCAyMC44MTgyIDIzLjI3MzEgMjQuNzI1NiAyMy4yNzMxIDI5LjU0NTVDMjMuMjczMSAzMi4xNTIxIDI0LjQxNTggMzQuNDkxOCAyNi4yMjc2IDM2LjA5MUgyNi4xODIyQzI2LjU4MzggMzYuMDkxIDI2LjkwOTQgMzYuNDE2NiAyNi45MDk0IDM2LjgxODJDMjYuOTA5NCAzNy4yMTk5IDI2LjU4MzggMzcuNTQ1NSAyNi4xODIyIDM3LjU0NTVIMjAuMzY0QzE5Ljk2MjMgMzcuNTQ1NSAxOS42MzY3IDM3LjIxOTkgMTkuNjM2NyAzNi44MTgyQzE5LjYzNjcgMzYuNDE2NiAxOS45NjIzIDM2LjA5MSAyMC4zNjQgMzYuMDkxSDI0LjIwMDlDMjIuNzE0MSAzNC4zMjExIDIxLjgxODUgMzIuMDM3OSAyMS44MTg1IDI5LjU0NTVDMjEuODE4NSAyMy45MjIyIDI2LjM3NzEgMTkuMzYzNyAzMi4wMDA0IDE5LjM2MzdDMzcuNjIzNiAxOS4zNjM3IDQyLjE4MjIgMjMuOTIyMiA0Mi4xODIyIDI5LjU0NTVDNDIuMTgyMiAzMi4wMzc5IDQxLjI4NjYgMzQuMzIxMSAzOS43OTk4IDM2LjA5MUg0My42MzY3QzQ0LjAzODQgMzYuMDkxIDQ0LjM2NCAzNi40MTY2IDQ0LjM2NCAzNi44MTgyQzQ0LjM2NCAzNy4yMTk5IDQ0LjAzODQgMzcuNTQ1NSA0My42MzY3IDM3LjU0NTVIMzcuODE4NUMzNy40MTY5IDM3LjU0NTUgMzcuMDkxMyAzNy4yMTk5IDM3LjA5MTMgMzYuODE4MloiIGZpbGw9IiNDQjk0MzUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMjAuMzY0IDQxLjkwOTFDMTkuOTYyMyA0MS45MDkxIDE5LjYzNjcgNDIuMjM0NyAxOS42MzY3IDQyLjYzNjRDMTkuNjM2NyA0My4wMzgxIDE5Ljk2MjMgNDMuMzYzNyAyMC4zNjQgNDMuMzYzN0g0My42MzY3QzQ0LjAzODQgNDMuMzYzNyA0NC4zNjQgNDMuMDM4MSA0NC4zNjQgNDIuNjM2NEM0NC4zNjQgNDIuMjM0NyA0NC4wMzg0IDQxLjkwOTEgNDMuNjM2NyA0MS45MDkxSDIwLjM2NFoiIGZpbGw9IiNDQjk0MzUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPg0KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3M9Im0tMCBmdy1ib2xkIj5MaWJyYTwvcD4NCiAgICAgICAgICAgICAgICAgICAgPHNwYW4+MjMvMDkgYSAyMi8xMDwvc3Bhbj4NCiAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvYT4NCiAgICAgICAgPC9kaXY+DQogICAgICAgIDxkaXYgY2xhc3M9ImNvbC02IGNvbC1tZC00IHRleHQtY2VudGVyIHB4LTAgbXgtMCBteS0zIj4NCiAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1kaWEvaG9yb3Njb3BvLWRvLWRpYS1wYXJhLWVzY29ycGlhby8iPg0KICAgICAgICAgICAgICAgIDxkaXYgaWQ9InNpZ24tc2NvcnBpbyIgY2xhc3M9InNpZ24gc2lnbi1ncmVlbiI+DQogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNpcmNsZSBiZy1ncmVlbiI+DQogICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIHdpZHRoPSI2NCIgaGVpZ2h0PSI2NSIgdmlld0JveD0iMCAwIDY0IDY1IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgZmlsbD0iI0NGRkNFNSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxyZWN0IHg9IjAuNzI3MjczIiB5PSIxLjE4MTg2IiB3aWR0aD0iNjIuNTQ1NSIgaGVpZ2h0PSI2Mi41NDU1IiByeD0iMzEuMjcyNyIgc3Ryb2tlPSIjQTNGN0QxIiBzdHJva2Utd2lkdGg9IjEuNDU0NTUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBkPSJNMjIuMzg3IDIwLjM2MzlDMjIuMTM2MSAyMC4wNTAzIDIxLjY3ODQgMTkuOTk5NCAyMS4zNjQ4IDIwLjI1MDNDMjEuMDUxMSAyMC41MDEzIDIxLjAwMDMgMjAuOTU4OSAyMS4yNTEyIDIxLjI3MjZMMjQuMDAwOSAyNC43MDk3VjM5LjcyNzNIMjUuNDU1NVYyNC43MTkyTDI3LjgxNSAyMS45MDUxQzI4LjEwNjIgMjEuNTU3NyAyOC42NDA4IDIxLjU1ODUgMjguOTMxIDIxLjkwNjdMMzEuMjczNiAyNC43MTc5VjM5LjcyNzNIMzIuNzI4MlYyNC43MTc5TDM1LjA3ODYgMjEuODk3NUMzNS4zNjkzIDIxLjU0ODUgMzUuOTA1MiAyMS41NDg1IDM2LjE5NiAyMS44OTc1TDM4LjM3NzggMjQuNTE1NkMzOC40ODY3IDI0LjY0NjMgMzguNTQ2NCAyNC44MTExIDM4LjU0NjQgMjQuOTgxMlYzNi4wODg1TDM4LjU0NjQgMzYuMDkxVjM5LjUwNzFDMzguNTQ2NCA0MC4yNjg1IDM4Ljc3MTcgNDEuMDEyOSAzOS4xOTQxIDQxLjY0NjRDMzkuOTA5MyA0Mi43MTkzIDQxLjExMzUgNDMuMzYzNyA0Mi40MDI5IDQzLjM2MzdINDMuNjM3M1Y0NS4xOTQ0QzQzLjYzNzMgNDUuMzIzOSA0My43OTM5IDQ1LjM4ODggNDMuODg1NiA0NS4yOTcyTDQ2LjQ0MzUgNDIuNzM5M0M0Ni41MDAzIDQyLjY4MjUgNDYuNTAwMyA0Mi41OTA0IDQ2LjQ0MzUgNDIuNTMzNkw0My44ODU2IDM5Ljk3NTZDNDMuNzkzOSAzOS44ODQgNDMuNjM3MyAzOS45NDg5IDQzLjYzNzMgNDAuMDc4NVY0MS45MDkxSDQyLjQwMjlDNDEuNTk5OCA0MS45MDkxIDQwLjg0OTggNDEuNTA3OCA0MC40MDQzIDQwLjgzOTVDNDAuMTQxMyA0MC40NDUgNDAuMDAwOSAzOS45ODEzIDQwLjAwMDkgMzkuNTA3MUw0MC4wMDA5IDI0Ljk4MTJDNDAuMDAwOSAyNC40NzA4IDM5LjgyMiAyMy45NzY2IDM5LjQ5NTIgMjMuNTg0NUwzNy4zMTM0IDIwLjk2NjNDMzYuNDQxMSAxOS45MTk1IDM0LjgzMzQgMTkuOTE5NSAzMy45NjEyIDIwLjk2NjNMMzIuMDAwOSAyMy4zMTg2TDMwLjA0ODQgMjAuOTc1NkMyOS4xNzc5IDE5LjkzMDkgMjcuNTc0MSAxOS45Mjg1IDI2LjcwMDQgMjAuOTcwNUwyNC43NDE0IDIzLjMwNjlMMjIuMzg3IDIwLjM2MzlaIiBmaWxsPSIjMDhDMTg1Ii8+DQogICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz4NCiAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzPSJtLTAgZnctYm9sZCI+RXNjb3JwacOjbzwvcD4NCiAgICAgICAgICAgICAgICAgICAgPHNwYW4+MjMvMTAgYSAyMS8xMTwvc3Bhbj4NCiAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvYT4gDQogICAgICAgIDwvZGl2Pg0KICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtNiBjb2wtbWQtNCB0ZXh0LWNlbnRlciBweC0wIG14LTAgbXktMyI+DQogICAgICAgICAgICA8YSBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS1zYWdpdGFyaW8vIj4NCiAgICAgICAgICAgICAgICA8ZGl2IGlkPSJzaWduLXNhZ2l0dGFyaXVzIiBjbGFzcz0ic2lnbiBzaWduLXJlZCI+DQogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNpcmNsZSBiZy1yZWQiPg0KICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjUiIHZpZXdCb3g9IjAgMCA2NCA2NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cmVjdCB4PSIwLjcyNzI3MyIgeT0iMS4xODE4NiIgd2lkdGg9IjYyLjU0NTUiIGhlaWdodD0iNjIuNTQ1NSIgcng9IjMxLjI3MjciIGZpbGw9IiNGQUU5RjUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cmVjdCB4PSIwLjcyNzI3MyIgeT0iMS4xODE4NiIgd2lkdGg9IjYyLjU0NTUiIGhlaWdodD0iNjIuNTQ1NSIgcng9IjMxLjI3MjciIHN0cm9rZT0iI0Y2RDRFQiIgc3Ryb2tlLXdpZHRoPSIxLjQ1NDU1Ii8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTQyLjg1NDcgMjAuNTM5OEM0Mi44MTkyIDIwLjQ1NCA0Mi43NjY2IDIwLjM3MzcgNDIuNjk2OSAyMC4zMDRMNDIuMTgyNiAyMC4wOTA5SDMwLjU0NjNDMzAuMTQ0NiAyMC4wOTA5IDI5LjgxOSAyMC40MTY2IDI5LjgxOSAyMC44MTgyQzI5LjgxOSAyMS4yMTk5IDMwLjE0NDYgMjEuNTQ1NSAzMC41NDYzIDIxLjU0NTVINDAuNDI2OEwyNi45MDk5IDM1LjA2MjRMMjIuMzMzMiAzMC40ODU4QzIyLjA0OTIgMzAuMjAxOCAyMS41ODg3IDMwLjIwMTggMjEuMzA0NyAzMC40ODU4QzIxLjAyMDcgMzAuNzY5OCAyMS4wMjA3IDMxLjIzMDMgMjEuMzA0NyAzMS41MTQzTDI1Ljg4MTQgMzYuMDkwOUwxOC4zOTU2IDQzLjU3NjdDMTguMTExNiA0My44NjA3IDE4LjExMTYgNDQuMzIxMiAxOC4zOTU2IDQ0LjYwNTJDMTguNjc5NiA0NC44ODkyIDE5LjE0MDEgNDQuODg5MiAxOS40MjQxIDQ0LjYwNTJMMjYuOTA5OSAzNy4xMTk1TDMxLjQ4NjUgNDEuNjk2MUMzMS43NzA2IDQxLjk4MDEgMzIuMjMxIDQxLjk4MDEgMzIuNTE1MSA0MS42OTYxQzMyLjc5OTEgNDEuNDEyMSAzMi43OTkxIDQwLjk1MTYgMzIuNTE1MSA0MC42Njc2TDI3LjkzODQgMzYuMDkwOUw0MS40NTUzIDIyLjU3NFYzMS43MjczQzQxLjQ1NTMgMzIuMTI5IDQxLjc4MSAzMi40NTQ2IDQyLjE4MjYgMzIuNDU0NkM0Mi41ODQzIDMyLjQ1NDYgNDIuOTA5OSAzMi4xMjkgNDIuOTA5OSAzMS43MjczVjIwLjgxODJDNDIuOTA5OSAyMC43MTk2IDQyLjg5MDMgMjAuNjI1NiA0Mi44NTQ3IDIwLjUzOThaIiBmaWxsPSIjQzYzQzhBIi8+DQogICAgICAgICAgICAgICAgICAgICAgICA8L3N2Zz4NCiAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzPSJtLTAgZnctYm9sZCI+U2FnaXTDoXJpbzwvcD4NCiAgICAgICAgICAgICAgICAgICAgPHNwYW4+MjIvMTEgYSAyMS8xMjwvc3Bhbj4NCiAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgIDwvYT4NCiAgICAgICAgPC9kaXY+DQogICAgICAgIDxkaXYgY2xhc3M9ImNvbC02IGNvbC1tZC00IHRleHQtY2VudGVyIHB4LTAgbXgtMCBteS0zIj4NCiAgICAgICAgICAgIDxhIGhyZWY9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL2hvcm9zY29wby1kby1kaWEvaG9yb3Njb3BvLWRvLWRpYS1wYXJhLWNhcHJpY29ybmlvLyI+DQogICAgICAgICAgICAgICAgPGRpdiBpZD0ic2lnbi1jYXByaWNvcm4iIGNsYXNzPSJzaWduIHNpZ24tYmx1ZSI+DQogICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNpcmNsZSBiZy1ibHVlIj4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9IjY0IiBoZWlnaHQ9IjY1IiB2aWV3Qm94PSIwIDAgNjQgNjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgeD0iMC43MjcyNzMiIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBmaWxsPSIjRDdGNUY2Ii8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgeD0iMC43MjcyNzMiIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBzdHJva2U9IiNCM0VBRUUiIHN0cm9rZS13aWR0aD0iMS40NTQ1NSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0xOS42MzY3IDIwLjgxODJMMjAuNzU1OSAyMS4xOTEzQzIxLjQ1NDcgMjEuNDI0MiAyMi4wODk3IDIxLjgxNjcgMjIuNjEwNiAyMi4zMzc1QzIzLjUwMDQgMjMuMjI3NCAyNC4wMDA0IDI0LjQzNDMgMjQuMDAwNCAyNS42OTI3VjI1LjkwOTFNMjQuMDAwNCAzNi4wOTA5VjI1LjkwOTFNMjQuMDAwNCAyNS45MDkxTDI2LjM1NDIgMjIuOTY2OEMyNi43MjIyIDIyLjUwNjkgMjcuMTU4OSAyMi4xMDY0IDI3LjY0OSAyMS43Nzk3QzI4LjU4OTQgMjEuMTUyOCAyOS42OTQ0IDIwLjgxODIgMzAuODI0NiAyMC44MTgySDMxLjU0NjlDMzIuMzIzNCAyMC44MTgyIDMzLjA4OTIgMjAuOTk5IDMzLjc4MzcgMjEuMzQ2MkwzNC4xNTg3IDIxLjUzMzhDMzUuMTA1MSAyMi4wMDY5IDM1LjgyNDcgMjIuODM2NyAzNi4xNTkzIDIzLjg0MDRDMzYuMjk0OSAyNC4yNDcyIDM2LjM2NCAyNC42NzMyIDM2LjM2NCAyNS4xMDJWMzQuMTU0OUMzNi4zNjQgMzQuNDc1MyAzNi4zOTA1IDM0Ljc5NTMgMzYuNDQzMiAzNS4xMTE0TDM2Ljc5NjIgMzcuMjI5OUMzNi44Nzc1IDM3LjcxNzYgMzcuMDIyNSAzOC4xODkzIDM3LjIyNTMgMzguNjMzOU0zNy4yMjUzIDM4LjYzMzlMMzguMjc3MyAzNy4xODc0QzM4LjQ1NiAzNi45NDE2IDM4LjY1MzcgMzYuNzEwMyAzOC44Njg2IDM2LjQ5NTRMMzkuNjQxMyAzNS43MjI3QzQwLjMzNjkgMzUuMDI3MiA0MS4yODAzIDM0LjYzNjQgNDIuMjY0IDM0LjYzNjRINDIuMzA1MUM0My41ODI0IDM0LjYzNjQgNDQuNzY2MSAzNS4zMDY2IDQ1LjQyMzMgMzYuNDAxOUM0Ni4xMTQyIDM3LjU1MzQgNDYuMTE0MiAzOC45OTIxIDQ1LjQyMzMgNDAuMTQzNkM0NC43NjYxIDQxLjIzODkgNDMuNTgyNCA0MS45MDkxIDQyLjMwNTEgNDEuOTA5MUg0MS44NDRDNDAuNjYzNSA0MS45MDkxIDM5LjUzMTQgNDEuNDQwMiAzOC42OTY3IDQwLjYwNTVMMzguMzYwMiA0MC4yNjlDMzcuODg0NSAzOS43OTMzIDM3LjUwMTMgMzkuMjM4OCAzNy4yMjUzIDM4LjYzMzlaTTM3LjIyNTMgMzguNjMzOUwzMy4yMTM2IDQ0LjE1QzMyLjg5NDMgNDQuNTg5IDMyLjQ3NDIgNDQuOTQ0OSAzMS45ODg2IDQ1LjE4NzdDMzEuNTE4IDQ1LjQyMyAzMC45OTkyIDQ1LjU0NTUgMzAuNDczMSA0NS41NDU1SDI3LjYzNjciIHN0cm9rZT0iIzI4QTJCMiIgc3Ryb2tlLXdpZHRoPSIxLjQ1NDU1IiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPg0KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3M9Im0tMCBmdy1ib2xkIj5DYXByaWPDs3JuaW88L3A+DQogICAgICAgICAgICAgICAgICAgIDxzcGFuPjIyLzEyIGEgMjAvMDE8L3NwYW4+DQogICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2E+DQogICAgICAgIDwvZGl2Pg0KICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtNiBjb2wtbWQtNCB0ZXh0LWNlbnRlciBweC0wIG14LTAgbXktMyI+DQogICAgICAgICAgICA8YSBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici9ob3Jvc2NvcG8tZG8tZGlhL2hvcm9zY29wby1kby1kaWEtcGFyYS1hcXVhcmlvLyI+DQogICAgICAgICAgICAgICAgPGRpdiBpZD0ic2lnbi1hcXVhcml1cyIgY2xhc3M9InNpZ24gc2lnbi15ZWxsb3ciPg0KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjaXJjbGUgYmcteWVsbG93Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxzdmcgd2lkdGg9IjY0IiBoZWlnaHQ9IjY1IiB2aWV3Qm94PSIwIDAgNjQgNjUiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgeD0iMC43MjcyNzMiIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBmaWxsPSIjRjRFREQxIi8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHJlY3QgeD0iMC43MjcyNzMiIHk9IjEuMTgxODYiIHdpZHRoPSI2Mi41NDU1IiBoZWlnaHQ9IjYyLjU0NTUiIHJ4PSIzMS4yNzI3IiBzdHJva2U9IiNFOUQ3OUYiIHN0cm9rZS13aWR0aD0iMS40NTQ1NSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0zMS40MTg0IDI3Ljg5NzFDMzEuNzA5MyAyNy41MDkyIDMyLjI5MTEgMjcuNTA5MiAzMi41ODIgMjcuODk3MUwzNC42MTg0IDMwLjYxMjNDMzUuNDkxMSAzMS43NzU5IDM3LjIzNjUgMzEuNzc1OSAzOC4xMDkzIDMwLjYxMjNMNDAuMDM2NSAyOC4wNDI3QzQwLjM0MyAyNy42MzQgNDAuOTY0MyAyNy42NjA0IDQxLjIzNSAyOC4wOTM2TDQzLjc0NzEgMzIuMTEyOUM0My45NiAzMi40NTM1IDQ0LjQwODcgMzIuNTU3IDQ0Ljc0OTMgMzIuMzQ0MkM0NS4wODk5IDMyLjEzMTMgNDUuMTkzNCAzMS42ODI2IDQ0Ljk4MDUgMzEuMzQyTDQyLjQ2ODUgMjcuMzIyN0M0MS42NTYyIDI2LjAyMzEgMzkuNzkyNCAyNS45NDM5IDM4Ljg3MjggMjcuMTY5OUwzNi45NDU2IDI5LjczOTZDMzYuNjU0NyAzMC4xMjc0IDM2LjA3MjkgMzAuMTI3NCAzNS43ODIgMjkuNzM5NkwzMy43NDU2IDI3LjAyNDRDMzIuODcyOSAyNS44NjA4IDMxLjEyNzUgMjUuODYwOCAzMC4yNTQ3IDI3LjAyNDRMMjguMjE4NCAyOS43Mzk2QzI3LjkyNzUgMzAuMTI3NCAyNy4zNDU2IDMwLjEyNzQgMjcuMDU0NyAyOS43Mzk2TDI1LjEyNzUgMjcuMTY5OUMyNC4yMDggMjUuOTQzOSAyMi4zNDQyIDI2LjAyMzEgMjEuNTMxOSAyNy4zMjI3TDE5LjAxOTggMzEuMzQyQzE4LjgwNjkgMzEuNjgyNiAxOC45MTA1IDMyLjEzMTMgMTkuMjUxMSAzMi4zNDQyQzE5LjU5MTcgMzIuNTU3IDIwLjA0MDQgMzIuNDUzNSAyMC4yNTMzIDMyLjExMjlMMjIuNzY1MyAyOC4wOTM2QzIzLjAzNjEgMjcuNjYwNCAyMy42NTc0IDI3LjYzNCAyMy45NjM5IDI4LjA0MjdMMjUuODkxMSAzMC42MTIzQzI2Ljc2MzggMzEuNzc1OSAyOC41MDkzIDMxLjc3NTkgMjkuMzgyIDMwLjYxMjNMMzEuNDE4NCAyNy44OTcxWiIgZmlsbD0iI0NCOTQzNSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIGQ9Ik0zMS40MTg0IDM1Ljg5NzFDMzEuNzA5MyAzNS41MDkyIDMyLjI5MTEgMzUuNTA5MiAzMi41ODIgMzUuODk3MUwzNC42MTg0IDM4LjYxMjNDMzUuNDkxMSAzOS43NzU5IDM3LjIzNjUgMzkuNzc1OSAzOC4xMDkzIDM4LjYxMjNMNDAuMDM2NSAzNi4wNDI3QzQwLjM0MyAzNS42MzQgNDAuOTY0MyAzNS42NjA0IDQxLjIzNSAzNi4wOTM2TDQzLjc0NzEgNDAuMTEyOUM0My45NiA0MC40NTM1IDQ0LjQwODcgNDAuNTU3IDQ0Ljc0OTMgNDAuMzQ0MkM0NS4wODk5IDQwLjEzMTMgNDUuMTkzNCAzOS42ODI2IDQ0Ljk4MDUgMzkuMzQyTDQyLjQ2ODUgMzUuMzIyN0M0MS42NTYyIDM0LjAyMzEgMzkuNzkyNCAzMy45NDM5IDM4Ljg3MjggMzUuMTY5OUwzNi45NDU2IDM3LjczOTVDMzYuNjU0NyAzOC4xMjc0IDM2LjA3MjkgMzguMTI3NCAzNS43ODIgMzcuNzM5NUwzMy43NDU2IDM1LjAyNDRDMzIuODcyOSAzMy44NjA4IDMxLjEyNzUgMzMuODYwOCAzMC4yNTQ3IDM1LjAyNDRMMjguMjE4NCAzNy43Mzk1QzI3LjkyNzUgMzguMTI3NCAyNy4zNDU2IDM4LjEyNzQgMjcuMDU0NyAzNy43Mzk1TDI1LjEyNzUgMzUuMTY5OUMyNC4yMDggMzMuOTQzOSAyMi4zNDQyIDM0LjAyMzEgMjEuNTMxOSAzNS4zMjI3TDE5LjAxOTggMzkuMzQyQzE4LjgwNjkgMzkuNjgyNiAxOC45MTA1IDQwLjEzMTMgMTkuMjUxMSA0MC4zNDQyQzE5LjU5MTcgNDAuNTU3IDIwLjA0MDQgNDAuNDUzNSAyMC4yNTMzIDQwLjExMjlMMjIuNzY1MyAzNi4wOTM2QzIzLjAzNjEgMzUuNjYwNCAyMy42NTc0IDM1LjYzNCAyMy45NjM5IDM2LjA0MjdMMjUuODkxMSAzOC42MTIzQzI2Ljc2MzggMzkuNzc1OSAyOC41MDkzIDM5Ljc3NTkgMjkuMzgyIDM4LjYxMjNMMzEuNDE4NCAzNS44OTcxWiIgZmlsbD0iI0NCOTQzNSIvPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+DQogICAgICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgICAgICA8cCBjbGFzcz0ibS0wIGZ3LWJvbGQiPkFxdcOhcmlvPC9wPg0KICAgICAgICAgICAgICAgICAgICA8c3Bhbj4yMS8wMSBhIDE5LzAyPC9zcGFuPg0KICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC9hPg0KICAgICAgICA8L2Rpdj4NCiAgICAgICAgPGRpdiBjbGFzcz0iY29sLTYgY29sLW1kLTQgdGV4dC1jZW50ZXIgcHgtMCBteC0wIG15LTMiPg0KICAgICAgICAgICAgPGEgaHJlZj0iaHR0cHM6Ly9qb2FvYmlkdS5jb20uYnIvaG9yb3Njb3BvLWRvLWRpYS9ob3Jvc2NvcG8tZG8tZGlhLXBhcmEtcGVpeGVzLyI+DQogICAgICAgICAgICAgICAgPGRpdiBpZD0ic2lnbi1waXNjZXMiIGNsYXNzPSJzaWduIHNpZ24tZ3JlZW4iPg0KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJjaXJjbGUgYmctZ3JlZW4iPg0KICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjUiIHZpZXdCb3g9IjAgMCA2NCA2NSIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cmVjdCB4PSIwLjcyNzI3MyIgeT0iMS4xODE4NiIgd2lkdGg9IjYyLjU0NTUiIGhlaWdodD0iNjIuNTQ1NSIgcng9IjMxLjI3MjciIGZpbGw9IiNDRkZDRTUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cmVjdCB4PSIwLjcyNzI3MyIgeT0iMS4xODE4NiIgd2lkdGg9IjYyLjU0NTUiIGhlaWdodD0iNjIuNTQ1NSIgcng9IjMxLjI3MjciIHN0cm9rZT0iI0EzRjdEMSIgc3Ryb2tlLXdpZHRoPSIxLjQ1NDU1Ii8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPHBhdGggZD0iTTI0LjQwMjUgMTguNzEzM0MyNC43NjE3IDE4LjUzMzcgMjUuMTk4NiAxOC42NzkzIDI1LjM3ODIgMTkuMDM4NkwyNi44OTg2IDIyLjA3OTRDMjguMTkxNyAyNC42NjU2IDI4LjkyMzEgMjcuNDU1IDI5LjA4ODkgMzAuMjcyOUgzNC45MTJDMzUuMDc3NyAyNy40NTUgMzUuODA5MSAyNC42NjU2IDM3LjEwMjMgMjIuMDc5NEwzOC42MjI3IDE5LjAzODZDMzguODAyMyAxOC42NzkzIDM5LjIzOTIgMTguNTMzNyAzOS41OTg0IDE4LjcxMzNDMzkuOTU3NyAxOC44OTMgNDAuMTAzMyAxOS4zMjk4IDM5LjkyMzcgMTkuNjg5MUwzOC40MDMyIDIyLjcyOTlDMzcuMjEyNCAyNS4xMTE2IDM2LjUzMzIgMjcuNjc4NCAzNi4zNjkzIDMwLjI3MjlINDAuNzI3N0M0MS4xMjk0IDMwLjI3MjkgNDEuNDU1IDMwLjU5ODUgNDEuNDU1IDMxLjAwMDJDNDEuNDU1IDMxLjQwMTggNDEuMTI5NCAzMS43Mjc1IDQwLjcyNzcgMzEuNzI3NUgzNi4zMzE0QzM2LjM2MDQgMzQuMzcwNyAzNi45MjI5IDM3LjAxMjIgMzguMDIyNiAzOS40ODY2TDM5LjkzNzggNDMuNzk1N0M0MC4xMDA5IDQ0LjE2MjggMzkuOTM1NiA0NC41OTI1IDM5LjU2ODUgNDQuNzU1N0MzOS4yMDE1IDQ0LjkxODggMzguNzcxNyA0NC43NTM1IDM4LjYwODYgNDQuMzg2NUwzNi42OTM0IDQwLjA3NzRDMzUuNTEgMzcuNDE0NyAzNC45MDU4IDM0LjU3MTggMzQuODc2NyAzMS43Mjc1SDI5LjEyNDJDMjkuMDk1MSAzNC41NzE4IDI4LjQ5MDkgMzcuNDE0NyAyNy4zMDc1IDQwLjA3NzRMMjUuMzkyMyA0NC4zODY1QzI1LjIyOTIgNDQuNzUzNSAyNC43OTk0IDQ0LjkxODggMjQuNDMyMyA0NC43NTU3QzI0LjA2NTMgNDQuNTkyNSAyMy45IDQ0LjE2MjggMjQuMDYzMSA0My43OTU3TDI1Ljk3ODMgMzkuNDg2NkMyNy4wNzggMzcuMDEyMiAyNy42NDA1IDM0LjM3MDcgMjcuNjY5NSAzMS43Mjc1SDIzLjI3MzJDMjIuODcxNSAzMS43Mjc1IDIyLjU0NTkgMzEuNDAxOCAyMi41NDU5IDMxLjAwMDJDMjIuNTQ1OSAzMC41OTg1IDIyLjg3MTUgMzAuMjcyOSAyMy4yNzMyIDMwLjI3MjlIMjcuNjMxNkMyNy40Njc3IDI3LjY3ODQgMjYuNzg4NSAyNS4xMTE2IDI1LjU5NzYgMjIuNzI5OUwyNC4wNzcyIDE5LjY4OTFDMjMuODk3NiAxOS4zMjk4IDI0LjA0MzIgMTguODkzIDI0LjQwMjUgMTguNzEzM1oiIGZpbGw9IiMwOEMxODUiLz4NCiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3ZnPg0KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3M9Im0tMCBmdy1ib2xkIj5QZWl4ZXM8L3A+DQogICAgICAgICAgICAgICAgICAgIDxzcGFuPjIwLzAyIGEgMjAvMDM8L3NwYW4+DQogICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8L2E+DQogICAgICAgIDwvZGl2Pg0KICAgICAgICANCiAgICA8L2Rpdj4NCjwvc2VjdGlvbj4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICAgICAgPCEtLSBFTkQgQ09OVEVOVCAtLT4gDQoNCg0KICAgICAgICAgICAgICAgIDwhLS0gU0lERSBCQVIgLS0+IA0KICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0xMiBjb2wtbWQtNCBweS1tZC0zIj4NCiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iYWRzIHNpZGVyYmFyLXRvcCAgZC1ub25lIGQtbWQtYmxvY2siIHN0eWxlPSJoZWlnaHQ6MzUwcHg7Ij4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3M9ImFkcy10aXRsZSI+UHVibGljaWRhZGU8L2Rpdj4NCg0KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCg0KICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgICAgIDwhLS0gRU5EIFNJREUgQkFSIC0tPiANCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L3NlY3Rpb24+DQo8L21haW4+DQo8Zm9vdGVyPg0KICAgIDxkaXYgY2xhc3M9ImNvbnRhaW5lciBmb290ZXItY29udGFpbmVyIj4NCiAgICAgICAgPGRpdiBjbGFzcz0icm93Ij4NCiAgICAgICAgICAgIDxkaXYgY2xhc3M9ImNvbC0xMiBteS0yIj4NCiAgICAgICAgICAgICAgICA8YSBocmVmPSJodHRwczovL2pvYW9iaWR1LmNvbS5iciI+DQogICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaW1hZ2VzL2pvYW8tYmlkdS1pbnRlcm5hY2lvbmFsLWxvZ28uc3ZnIiAvPg0KICAgICAgICAgICAgICAgIDwvYT4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L2Rpdj4NCg0KICAgICAgICA8ZGl2IGNsYXNzPSJyb3ciPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sLTEyIG15LTIiPg0KICAgICAgICAgICAgICAgIDx1bCBjbGFzcz0ibmF2IGp1c3RpZnktY29udGVudC1jZW50ZXIgYm9yZGVyLWJvdHRvbSBwYi0zIj4NCiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJuYXYtaXRlbSI+DQogICAgICAgICAgICAgICAgICAgICAgICA8YSBocmVmPSIjIiBjbGFzcz0ibmF2LWxpbmsgcHgtMiI+Sm/Do28gQmlkdSBCcmFzaWw8L2E+DQogICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibmF2LWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iIyIgY2xhc3M9Im5hdi1saW5rIHB4LTIiPkpvw6NvIEJpZHUgQ2hpbGU8L2E+DQogICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibmF2LWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iIyIgY2xhc3M9Im5hdi1saW5rIHB4LTIiPkpvw6NvIEJpZHUgQ29sw7RtYmlhPC9hPg0KICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Im5hdi1pdGVtIj4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiIGNsYXNzPSJuYXYtbGluayBweC0yIj5Kb8OjbyBCaWR1IEVzcGFuaGE8L2E+DQogICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibmF2LWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iIyIgY2xhc3M9Im5hdi1saW5rIHB4LTIiPkpvw6NvIEJpZHUgVVNBPC9hPg0KICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Im5hdi1pdGVtIj4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiIGNsYXNzPSJuYXYtbGluayBweC0yIj5Kb8OjbyBCaWR1IE3DqXhpY288L2E+DQogICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibmF2LWl0ZW0iPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iIyIgY2xhc3M9Im5hdi1saW5rIHB4LTIiPkpvw6NvIEJpZHUgUG9ydHVnYWw8L2E+DQogICAgICAgICAgICAgICAgICAgIDwvbGk+DQogICAgICAgICAgICAgICAgPC91bD4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29sLTEyIG15LTIiPg0KICAgICAgICAgICAgICAgIDx1bCBjbGFzcz0ibmF2IGp1c3RpZnktY29udGVudC1jZW50ZXIgIj4NCiAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJuYXYtaXRlbSI+PGEgaHJlZj0iIyIgY2xhc3M9Im5hdi1saW5rIHB4LTIiPlBvbMOtdGljYSBkZSBDb29raWVzPC9hPjwvbGk+DQogICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibmF2LWl0ZW0iPjxhIGhyZWY9IiMiIGNsYXNzPSJuYXYtbGluayBweC0yIj5Qb2zDrXRpY2EgZGUgUHJpdmFjaWRhZDwvYT48L2xpPg0KICAgICAgICAgICAgICAgICAgICA8bGkgY2xhc3M9Im5hdi1pdGVtIj48YSBocmVmPSIjIiBjbGFzcz0ibmF2LWxpbmsgcHgtMiI+Q29uZGljaW9uZXMgZGUgdXNvPC9hPjwvbGk+DQogICAgICAgICAgICAgICAgPC91bD4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L2Rpdj4NCg0KICAgICAgICA8ZGl2IGNsYXNzPSJyb3cganVzdGlmeS1jb250ZW50LWNlbnRlciI+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtMTIgbXktMiI+DQogICAgICAgICAgICAgICAgPGRpdiBjbGFzcz0ic29jaWFsLWljb25zIj4NCiAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzPSJsaXN0LWdyb3VwIGxpc3QtZ3JvdXAtaG9yaXpvbnRhbCBqdXN0aWZ5LWNvbnRlbnQtY2VudGVyIj4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIGQtbm9uZSBkLW1kLWJsb2NrIHB4LTEiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvc29jaWFsL2ZhY2Vib29rLnN2ZyIgLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0gZC1ub25lIGQtbWQtYmxvY2sgcHgtMSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iIyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9zb2NpYWwvaW5zdGFncmFtLnN2ZyIgLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0gZC1ub25lIGQtbWQtYmxvY2sgcHgtMSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iIyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9zb2NpYWwvcGludGVyZXN0LnN2ZyIgLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0gZC1ub25lIGQtbWQtYmxvY2sgcHgtMSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iIyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9zb2NpYWwvdGlrdG9rLnN2ZyIgLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICAgICAgPGxpIGNsYXNzPSJsaXN0LWdyb3VwLWl0ZW0gZC1ub25lIGQtbWQtYmxvY2sgcHgtMSI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGEgaHJlZj0iIyI+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPGltZyBzcmM9Imh0dHBzOi8vam9hb2JpZHUuY29tLmJyL3dwLWNvbnRlbnQvdGhlbWVzL3l3LWpiaW5jL2Fzc2V0cy9pY29ucy9zb2NpYWwveW91dHViZS5zdmciIC8+DQogICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9hPg0KICAgICAgICAgICAgICAgICAgICAgICAgPC9saT4NCiAgICAgICAgICAgICAgICAgICAgICAgIDxsaSBjbGFzcz0ibGlzdC1ncm91cC1pdGVtIGQtbm9uZSBkLW1kLWJsb2NrIHB4LTEiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9IiMiPg0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxpbWcgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9hc3NldHMvaWNvbnMvc29jaWFsL3doYXRzYXBwLnN2ZyIgLz4NCiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2E+DQogICAgICAgICAgICAgICAgICAgICAgICA8L2xpPg0KICAgICAgICAgICAgICAgICAgICA8L3VsPg0KICAgICAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgICA8ZGl2IGNsYXNzPSJjb2wtMTIgbXQtMiI+DQogICAgICAgICAgICAgICAgPHA+Q29weXJpZ2h0IMKpIDIwMjUgQXN0cmFsIERpZ2l0YWwuIFRvZG9zIGRpcmVpdG9zIHJlc2VydmFkb3MsIHByb2liaWRvIHF1YWxxdWVyIHRpcG8gZGUgY8OzcGlhIG91IHJlcHJvZHXDp8OjbyBzZW0gcHLDqXZpYSBhdXRvcml6YcOnw6NvLjwvcD4NCiAgICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L2Rpdj4NCiAgICAgICAgDQogICAgPC9kaXY+DQo8L2Zvb3Rlcj4NCjxzY3JpcHQgc3JjPSJodHRwczovL2NvZGUuanF1ZXJ5LmNvbS9qcXVlcnktMy43LjEuc2xpbS5taW4uanMiIGludGVncml0eT0ic2hhMjU2LWttSHZzMEIrT3BDVzVHVkhVTmp2OXJPbVkwSXZTSVJjZjd6R1VEVERRTTg9IiBjcm9zc29yaWdpbj0iYW5vbnltb3VzIj48L3NjcmlwdD4NCjxzY3JpcHQgc3JjPSJodHRwczovL2pvYW9iaWR1LmNvbS5ici93cC1jb250ZW50L3RoZW1lcy95dy1qYmluYy9ub2RlX21vZHVsZXMvYm9vdHN0cmFwL2Rpc3QvanMvYm9vdHN0cmFwLmJ1bmRsZS5taW4uanMiPjwvc2NyaXB0Pg0KPHNjcmlwdCBzcmM9Ii9zdGF0aWMvanMvbGF6eXNjcmlwdC5taW4uanMiPjwvc2NyaXB0PiA=
  recorded_at: Fri, 06 Jun 2025 12:35:28 GMT
recorded_with: VCR 6.3.1
