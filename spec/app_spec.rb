# frozen_string_literal: true

require 'spec_helper'

RSpec.describe 'FunctionsFramework', type: :function do
  describe 'sign' do
    let(:request) { make_post_request('https://example.com/foo', params, headers) }

    context 'when the request is a challenge' do
      let(:headers) { ['Content-Type: application/json'] }
      let(:params) { File.read('spec/fixtures/slack-challenge-request.json') }

      it 'returns successfull with challenge in body' do
        load_temporary 'app.rb' do
          response = call_http 'sign', request

          expect(response).to have_attributes(status: 200,
                                              body: ['8UYwKDyQtZ2uu8id99ppfm3LhVK0U5DvlXMbphmztSZ8nowJfTGQ'])
        end
      end
    end

    context 'when the request is a private sign request' do
      let(:headers) { ['Content-Type: application/x-www-form-urlencoded'] }
      let(:params) do
        'token=JdBZFq5n9sKFEdFzmoJUwDsr&' \
          'team_id=T027EHYC7&' \
          'team_domain=parafuzo&' \
          'channel_id=C06A84LMNN8&' \
          'channel_name=testes-do-leo&' \
          'user_id=U549VCZGS&' \
          'user_name=vyper&' \
          'command=hor%C3%B3scopo&' \
          'text=virgem&' \
          'api_app_id=A06A81GAJQL&' \
          'is_enterprise_install=false&' \
          'response_url=https://hooks.slack.com/commands/T027EHYC7/6342830843381/sSrOKjn9UI0rV41zZUMKIt2N&' \
          'trigger_id=6345651465555.2252610415.bf489b5f31f2428326b0dd97d7b4e2cf'
      end

      it 'returns successfull sign response in body' do # rubocop:disable RSpec/ExampleLength
        load_temporary 'app.rb' do
          VCR.use_cassette 'private-sign-request' do
            response = call_http 'sign', request

            expect(response).to have_attributes(
              status: 200,
              body: [
                "Segue o horóscopo de hoje para virgem :virgo::\n\n> Dinhneiro - Sua habilidade para lidar com " \
                'dinheiro segue em alta e pode surgir a oportunidade de pegar um serviço extra para dar uma ' \
                'reforçada no seu bolso. Separe um tempinho para organizar suas contas, detalhar o orçamento e ' \
                "planejar as próximas compras.\n> \n> Trabalho - Bom momento para negociar um aumento ou ir atrás de " \
                "uma promoção.\n> \n> Amor - Mas a entrada de Vênus em Touro também deixa o astral mais " \
                'descontraído, inclusive na vida amorosa. Manter o bom humor será a chave para brilhar nos assuntos ' \
                "do coração.\n> \n> Palpite do dia: 53, 60, 44\n> \n> Cor para o dia: CREME\n\nFonte: " \
                '<https://joaobidu.com.br|João Bidu>'
              ]
            )
          end
        end
      end
    end

    context 'when the request is a public sign request' do
      let(:headers) { ['Content-Type: application/json'] }
      let(:params) { File.read('spec/fixtures/slack-public-sign-request.json') }

      it 'returns successfull sign response in body' do # rubocop:disable RSpec/ExampleLength
        load_temporary 'app.rb' do
          VCR.use_cassette 'public-sign-request' do
            response = call_http 'sign', request

            expect(response).to have_attributes(status: 200, body: ['ok'])
          end
        end
      end
    end
  end
end
